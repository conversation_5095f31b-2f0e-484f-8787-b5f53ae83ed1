# RDAgent Project Status Script
param(
    [string]$Action = "status",
    [string]$ProjectRoot = "d:\PycharmProjects\my_rdagent\rdagent_integration"
)

Write-Host "RDAgent Project Manager" -ForegroundColor Green
Write-Host "=" * 40 -ForegroundColor Cyan
Write-Host "Action: $Action" -ForegroundColor Gray
Write-Host "Project: $ProjectRoot" -ForegroundColor Gray
Write-Host "Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

function Write-StatusLog {
    param([string]$Message, [string]$Type = "INFO")
    $color = switch($Type) {
        "SUCCESS" {"Green"}
        "WARNING" {"Yellow"}
        "ERROR" {"Red"}
        "INFO" {"Cyan"}
        default {"White"}
    }
    Write-Host $Message -ForegroundColor $color
}

function Get-SystemStatus {
    Write-StatusLog "Checking system status..." "INFO"
    
    $status = @{
        ProjectExists = Test-Path $ProjectRoot
        WebService = $false
        PythonProcesses = 0
        MemoryUsage = 0
        DiskSpace = 0
        Timestamp = Get-Date
    }
    
    # Check web service
    try {
        $webStatus = Test-NetConnection -ComputerName localhost -Port 8000 -InformationLevel Quiet -WarningAction SilentlyContinue
        $status.WebService = $webStatus
    } catch {
        $status.WebService = $false
    }
    
    # Check Python processes
    try {
        $pythonProcesses = Get-Process python -ErrorAction SilentlyContinue
        $status.PythonProcesses = $pythonProcesses.Count
    } catch {
        $status.PythonProcesses = 0
    }
    
    # Check memory usage
    try {
        $memory = Get-WmiObject -Class Win32_OperatingSystem
        $totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
        $freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
        $status.MemoryUsage = [math]::Round((($totalMemoryGB - $freeMemoryGB) / $totalMemoryGB) * 100, 2)
    } catch {
        $status.MemoryUsage = 0
    }
    
    # Check disk space
    try {
        $driveLetter = (Split-Path $ProjectRoot -Qualifier).TrimEnd(':')
        $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='${driveLetter}:'"
        if ($disk) {
            $status.DiskSpace = [math]::Round(($disk.FreeSpace / $disk.Size) * 100, 2)
        }
    } catch {
        $status.DiskSpace = 0
    }
    
    return $status
}

function Show-Status {
    $status = Get-SystemStatus
    
    Write-StatusLog "System Status Report" "SUCCESS"
    Write-Host "-" * 30 -ForegroundColor Gray
    
    # Project status
    if ($status.ProjectExists) {
        Write-StatusLog "Project Path: OK" "SUCCESS"
    } else {
        Write-StatusLog "Project Path: NOT FOUND" "ERROR"
    }
    
    # Web service status
    if ($status.WebService) {
        Write-StatusLog "Web Service (Port 8000): RUNNING" "SUCCESS"
    } else {
        Write-StatusLog "Web Service (Port 8000): NOT RUNNING" "WARNING"
    }
    
    # Python processes
    Write-StatusLog "Python Processes: $($status.PythonProcesses)" "INFO"
    
    # Memory usage
    $memoryColor = if ($status.MemoryUsage -gt 80) {"ERROR"} elseif ($status.MemoryUsage -gt 60) {"WARNING"} else {"SUCCESS"}
    Write-StatusLog "Memory Usage: $($status.MemoryUsage)%" $memoryColor
    
    # Disk space
    $diskColor = if ($status.DiskSpace -lt 20) {"ERROR"} elseif ($status.DiskSpace -lt 40) {"WARNING"} else {"SUCCESS"}
    Write-StatusLog "Disk Free Space: $($status.DiskSpace)%" $diskColor
    
    Write-StatusLog "Last Updated: $($status.Timestamp.ToString('HH:mm:ss'))" "INFO"
    
    return $status
}

function Start-RDAgent {
    Write-StatusLog "Starting RDAgent system..." "INFO"
    
    # Check if already running
    $status = Get-SystemStatus
    if ($status.WebService) {
        Write-StatusLog "Web service is already running on port 8000" "WARNING"
        return
    }
    
    # Check Python environment
    $pythonPath = "$ProjectRoot\venv\Scripts\python.exe"
    if (-not (Test-Path $pythonPath)) {
        $pythonPath = "python"
    }
    
    # Create a simple web server script
    $webServerScript = "$ProjectRoot\temp_web_server.py"
    $webServerContent = @"
import uvicorn
from fastapi import FastAPI
from datetime import datetime
import json

app = FastAPI(title="RDAgent API", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "RDAgent is running", "timestamp": datetime.now().isoformat()}

@app.get("/api/status")
async def get_status():
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/api/health")
async def health_check():
    return {
        "healthy": True,
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    print("Starting RDAgent Web Server...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
"@
    
    $webServerContent | Out-File $webServerScript -Encoding UTF8
    
    try {
        # Try to install required packages
        & $pythonPath -m pip install fastapi uvicorn --quiet 2>$null
        
        # Start the web server
        Write-StatusLog "Starting web server on port 8000..." "INFO"
        Start-Process -FilePath $pythonPath -ArgumentList $webServerScript -WindowStyle Hidden
        
        # Wait a moment and check if it started
        Start-Sleep 3
        $newStatus = Get-SystemStatus
        
        if ($newStatus.WebService) {
            Write-StatusLog "Web server started successfully!" "SUCCESS"
            Write-StatusLog "Access the web interface at: http://localhost:8000" "INFO"
        } else {
            Write-StatusLog "Web server may have failed to start" "WARNING"
        }
        
    } catch {
        Write-StatusLog "Failed to start web server: $($_.Exception.Message)" "ERROR"
    }
}

function Stop-RDAgent {
    Write-StatusLog "Stopping RDAgent system..." "INFO"
    
    try {
        # Stop Python processes related to the project
        $pythonProcesses = Get-Process python -ErrorAction SilentlyContinue | Where-Object {
            $_.Path -like "*rdagent*" -or 
            (Get-NetTCPConnection -OwningProcess $_.Id -ErrorAction SilentlyContinue | Where-Object {$_.LocalPort -eq 8000})
        }
        
        if ($pythonProcesses) {
            $pythonProcesses | ForEach-Object {
                Write-StatusLog "Stopping process: $($_.ProcessName) (PID: $($_.Id))" "INFO"
                Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
            }
            Write-StatusLog "Stopped $($pythonProcesses.Count) processes" "SUCCESS"
        } else {
            Write-StatusLog "No running processes found" "INFO"
        }
        
        # Clean up temporary files
        $tempScript = "$ProjectRoot\temp_web_server.py"
        if (Test-Path $tempScript) {
            Remove-Item $tempScript -Force -ErrorAction SilentlyContinue
        }
        
    } catch {
        Write-StatusLog "Error stopping services: $($_.Exception.Message)" "ERROR"
    }
}

function Clean-Project {
    Write-StatusLog "Cleaning project..." "INFO"
    
    $cleanupPaths = @(
        "$ProjectRoot\logs\*.log",
        "$ProjectRoot\temp_*",
        "$ProjectRoot\__pycache__",
        "$ProjectRoot\*.pyc"
    )
    
    $cleanedCount = 0
    foreach ($path in $cleanupPaths) {
        try {
            $items = Get-ChildItem $path -Recurse -ErrorAction SilentlyContinue
            if ($items) {
                $items | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
                $cleanedCount += $items.Count
            }
        } catch {
            # Ignore cleanup errors
        }
    }
    
    Write-StatusLog "Cleaned $cleanedCount items" "SUCCESS"
}

function Create-MissingDirectories {
    Write-StatusLog "Creating missing directories..." "INFO"
    
    $requiredDirs = @("data", "configs", "results", "reports", "backups")
    $createdCount = 0
    
    foreach ($dir in $requiredDirs) {
        $fullPath = Join-Path $ProjectRoot $dir
        if (-not (Test-Path $fullPath)) {
            try {
                New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
                Write-StatusLog "Created directory: $dir" "SUCCESS"
                $createdCount++
            } catch {
                Write-StatusLog "Failed to create directory: $dir" "ERROR"
            }
        }
    }
    
    if ($createdCount -eq 0) {
        Write-StatusLog "All required directories already exist" "INFO"
    } else {
        Write-StatusLog "Created $createdCount directories" "SUCCESS"
    }
}

# Main execution
switch ($Action.ToLower()) {
    "status" {
        Show-Status | Out-Null
    }
    "start" {
        Start-RDAgent
        Write-Host ""
        Show-Status | Out-Null
    }
    "stop" {
        Stop-RDAgent
        Write-Host ""
        Show-Status | Out-Null
    }
    "clean" {
        Clean-Project
    }
    "setup" {
        Create-MissingDirectories
    }
    "restart" {
        Stop-RDAgent
        Start-Sleep 2
        Start-RDAgent
        Write-Host ""
        Show-Status | Out-Null
    }
    default {
        Write-StatusLog "Unknown action: $Action" "ERROR"
        Write-StatusLog "Available actions: status, start, stop, clean, setup, restart" "INFO"
    }
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
