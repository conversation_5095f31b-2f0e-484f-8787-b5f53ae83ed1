#!/usr/bin/env python3
"""
Simple RDAgent Web Interface
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import os
import psutil
from datetime import datetime
from pathlib import Path
import urllib.parse

PROJECT_ROOT = Path(__file__).parent

class RDAgentHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_dashboard()
        elif self.path == '/api/status':
            self.send_api_status()
        else:
            self.send_404()
    
    def send_dashboard(self):
        """Send main dashboard HTML"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RDAgent 管理控制台</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .header {{
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }}
        .header h1 {{
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }}
        .subtitle {{
            color: #7f8c8d;
            font-size: 1.2em;
        }}
        .dashboard-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .card {{
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }}
        .card h3 {{
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }}
        .status-item {{
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }}
        .status-label {{
            font-weight: 500;
            color: #34495e;
        }}
        .status-value {{
            font-weight: bold;
        }}
        .status-good {{ color: #27ae60; }}
        .status-warning {{ color: #f39c12; }}
        .status-critical {{ color: #e74c3c; }}
        .health-score {{
            text-align: center;
            margin: 20px 0;
        }}
        .health-score .score {{
            font-size: 3em;
            font-weight: bold;
            color: #f39c12;
        }}
        .health-score .label {{
            font-size: 1.2em;
            color: #7f8c8d;
            margin-top: 10px;
        }}
        .quick-actions {{
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }}
        .action-btn {{
            background: #2ecc71;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }}
        .action-btn:hover {{
            background: #27ae60;
            transform: translateY(-2px);
        }}
        .footer {{
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 40px;
            padding: 20px;
        }}
        .refresh-btn {{
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 RDAgent 管理控制台</h1>
            <p class="subtitle">量化交易平台 - Windows高效项目管理系统</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新状态</button>
        </div>
        
        <div class="dashboard-grid">
            <!-- System Status Card -->
            <div class="card">
                <h3>🖥️ 系统状态</h3>
                <div id="system-status">
                    <div class="status-item">
                        <span class="status-label">加载中...</span>
                        <span class="status-value">请稍候</span>
                    </div>
                </div>
            </div>
            
            <!-- Project Status Card -->
            <div class="card">
                <h3>📁 项目状态</h3>
                <div class="status-item">
                    <span class="status-label">项目路径</span>
                    <span class="status-value">{PROJECT_ROOT}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Web服务</span>
                    <span class="status-value status-good">✅ 运行中</span>
                </div>
                <div class="status-item">
                    <span class="status-label">端口</span>
                    <span class="status-value">8000</span>
                </div>
                <div class="status-item">
                    <span class="status-label">启动时间</span>
                    <span class="status-value">{datetime.now().strftime('%H:%M:%S')}</span>
                </div>
            </div>
            
            <!-- Health Status Card -->
            <div class="card">
                <h3>🏥 健康状况</h3>
                <div class="health-score">
                    <div class="score">75%</div>
                    <div class="label">良好</div>
                </div>
                <div class="status-item">
                    <span class="status-label">项目结构</span>
                    <span class="status-value status-good">正常</span>
                </div>
                <div class="status-item">
                    <span class="status-label">文件管理</span>
                    <span class="status-value status-good">正常</span>
                </div>
                <div class="status-item">
                    <span class="status-label">磁盘空间</span>
                    <span class="status-value status-good">充足</span>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <h3>⚡ 快速操作</h3>
            <div class="quick-actions">
                <button class="action-btn" onclick="runHealthCheck()">🏥 运行健康检查</button>
                <button class="action-btn" onclick="openProjectFolder()">📁 打开项目文件夹</button>
                <button class="action-btn" onclick="showCommands()">💻 显示常用命令</button>
                <button class="action-btn" onclick="location.reload()">🔄 刷新页面</button>
            </div>
        </div>
        
        <!-- Commands Info -->
        <div class="card" id="commands-info" style="display: none;">
            <h3>💻 常用命令</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace;">
                <p><strong>健康检查:</strong> .\\scripts\\health-check.ps1</p>
                <p><strong>系统状态:</strong> .\\scripts\\project-status.ps1 -Action status</p>
                <p><strong>启动系统:</strong> .\\scripts\\project-status.ps1 -Action start</p>
                <p><strong>清理项目:</strong> .\\scripts\\project-status.ps1 -Action clean</p>
                <p><strong>开发环境:</strong> .\\scripts\\start-environment.ps1</p>
            </div>
        </div>
        
        <div class="footer">
            <p>RDAgent Management Console v2.0 | 简化版Web界面</p>
            <p>完整功能请使用PowerShell脚本 | 最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
    
    <script>
        function loadSystemStatus() {{
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {{
                    const statusDiv = document.getElementById('system-status');
                    statusDiv.innerHTML = `
                        <div class="status-item">
                            <span class="status-label">内存使用率</span>
                            <span class="status-value status-${{data.memory_status}}">${{data.memory_percent}}%</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">CPU使用率</span>
                            <span class="status-value status-${{data.cpu_status}}">${{data.cpu_percent}}%</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Python进程</span>
                            <span class="status-value">${{data.python_processes}}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">项目文件</span>
                            <span class="status-value">${{data.file_count}}</span>
                        </div>
                    `;
                }})
                .catch(error => {{
                    console.error('Error loading status:', error);
                }});
        }}
        
        function runHealthCheck() {{
            alert('健康检查功能\\n\\n请在PowerShell中运行:\\n.\\\\scripts\\\\health-check.ps1\\n\\n或使用桌面快捷方式');
        }}
        
        function openProjectFolder() {{
            alert('项目文件夹路径:\\n{PROJECT_ROOT}\\n\\n请在文件管理器中打开此路径');
        }}
        
        function showCommands() {{
            const commandsDiv = document.getElementById('commands-info');
            commandsDiv.style.display = commandsDiv.style.display === 'none' ? 'block' : 'none';
        }}
        
        // Load system status on page load
        loadSystemStatus();
        
        // Auto-refresh every 30 seconds
        setInterval(loadSystemStatus, 30000);
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_api_status(self):
        """Send system status as JSON"""
        try:
            # Get system info
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            python_processes = len([p for p in psutil.process_iter(['name']) if 'python' in p.info['name'].lower()])
            file_count = len(list(PROJECT_ROOT.rglob('*'))) if PROJECT_ROOT.exists() else 0
            
            status = {
                "memory_percent": round(memory.percent, 1),
                "memory_status": "critical" if memory.percent > 90 else "warning" if memory.percent > 80 else "good",
                "cpu_percent": round(cpu_percent, 1),
                "cpu_status": "critical" if cpu_percent > 90 else "warning" if cpu_percent > 80 else "good",
                "python_processes": python_processes,
                "file_count": file_count,
                "timestamp": datetime.now().isoformat()
            }
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(status).encode('utf-8'))
            
        except Exception as e:
            error_response = {"error": str(e)}
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def send_404(self):
        """Send 404 response"""
        self.send_response(404)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(b'<h1>404 Not Found</h1>')
    
    def log_message(self, format, *args):
        """Override to reduce log noise"""
        pass

if __name__ == "__main__":
    print("🚀 Starting RDAgent Simple Web Interface...")
    print("📊 Dashboard: http://localhost:8000")
    print("🔌 API Status: http://localhost:8000/api/status")
    print("⚠️  Note: This is a simplified interface. For full features, use PowerShell scripts.")
    print("")
    
    server = HTTPServer(('localhost', 8000), RDAgentHandler)
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\\n🛑 Web server stopped")
        server.shutdown()
