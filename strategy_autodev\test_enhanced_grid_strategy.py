#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强网格交易策略测试脚本

基于华泰证券网格交易策略研究，验证增强功能的有效性

Author: AI Assistant
Date: 2024
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from strategy_autodev.core.unified_strategy_system import get_unified_system, StrategyCategory
    from strategy_autodev.core.enhanced_grid_trading_system import (
        GridType, GridTradingSuitabilityEvaluator, SymmetricGridGenerator
    )
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在正确的环境中运行此脚本")
    sys.exit(1)

try:
    from data_pipeline.adapter_manager import AdapterManager
    DATA_PIPELINE_AVAILABLE = True
except ImportError:
    print("警告: 数据管道模块不可用，将跳过相关测试")
    DATA_PIPELINE_AVAILABLE = False

def test_grid_suitability_evaluator():
    """
    测试网格交易适合度评价器
    """
    print("\n=== 测试网格交易适合度评价器 ===")
    
    # 生成模拟价格数据
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    
    # 模拟具有均值回归特征的价格序列
    base_price = 100
    prices = [base_price]
    
    for i in range(1, len(dates)):
        # 添加均值回归特征
        deviation = (prices[-1] - base_price) / base_price
        mean_reversion_force = -0.1 * deviation  # 回归力度
        random_shock = np.random.normal(0, 0.02)  # 随机冲击
        
        price_change = mean_reversion_force + random_shock
        new_price = prices[-1] * (1 + price_change)
        prices.append(max(new_price, 10))  # 防止价格过低
    
    price_series = pd.Series(prices, index=dates)
    
    # 创建适合度评价器
    evaluator = GridTradingSuitabilityEvaluator()
    
    try:
        # 评价适合度
        suitability = evaluator.evaluate_suitability(price_series)
        
        print(f"✅ 适合度评价完成:")
        print(f"  - 均值回归评分: {suitability.mean_reversion_score:.3f}")
        print(f"  - 波动率评分: {suitability.volatility_score:.3f}")
        print(f"  - 综合评分: {suitability.overall_score:.3f}")
        print(f"  - 推荐策略: {suitability.recommendation}")
        
        return True
        
    except Exception as e:
        print(f"❌ 适合度评价失败: {e}")
        return False

def test_symmetric_grid_generator():
    """
    测试对称网格生成器
    """
    print("\n=== 测试对称网格生成器 ===")
    
    generator = SymmetricGridGenerator()
    
    try:
        # 生成对称网格
        base_price = 100.0
        grid_spacing = 0.05  # 5%间距
        max_levels = 10
        
        grid_structure = generator.generate_grid(
            base_price=base_price,
            grid_spacing=grid_spacing,
            max_levels=max_levels,
            grid_type=GridType.SYMMETRIC
        )
        
        print(f"✅ 对称网格生成完成:")
        print(f"  - 基准价格: {grid_structure.base_price}")
        print(f"  - 网格类型: {grid_structure.grid_type}")
        print(f"  - 买入价格: {grid_structure.buy_levels[:5]}...")
        print(f"  - 卖出价格: {grid_structure.sell_levels[:5]}...")
        
        # 生成中心高密度网格
        center_dense_grid = generator.generate_grid(
            base_price=base_price,
            grid_spacing=grid_spacing,
            max_levels=max_levels,
            grid_type=GridType.CENTER_DENSE
        )
        
        print(f"\n✅ 中心高密度网格生成完成:")
        print(f"  - 买入价格: {center_dense_grid.buy_levels[:5]}...")
        print(f"  - 卖出价格: {center_dense_grid.sell_levels[:5]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 网格生成失败: {e}")
        return False

def test_unified_strategy_system():
    """
    测试统一策略管理系统
    """
    print("\n=== 测试统一策略管理系统 ===")
    
    try:
        # 获取统一系统
        system = get_unified_system()
        
        # 列出已注册的策略
        print("\n已注册的策略:")
        for name, info in system.strategies.items():
            if isinstance(info, dict):
                print(f"  - {name} ({info['category']}) - {info['description']}")
        
        # 测试增强网格交易策略
        if "EnhancedGridTrading" in system.strategies:
            print("\n=== 测试增强网格交易策略 ===")
            
            grid_params = {
                'grid_type': 'symmetric',
                'grid_density': 'high',
                'enable_suitability_filter': True,
                'enable_dynamic_adjustment': False,  # 暂时关闭动态调整以简化测试
                'min_suitability_score': 0.3,
                'base_position_weight': 0.1,
                'max_single_position_weight': 0.2,
                'grid_spacing': 0.05,
                'max_grid_levels': 8,
                'profit_take_ratio': 0.15,
                'stop_loss_ratio': 0.20
            }
            
            # 创建策略实例
            strategy = system.create_strategy("EnhancedGridTrading", grid_params)
            print("✅ 增强网格交易策略创建成功")
            
            return True
        else:
            print("❌ 增强网格交易策略未注册")
            return False
            
    except Exception as e:
        print(f"❌ 统一策略系统测试失败: {e}")
        return False

def test_data_pipeline_integration():
    """
    测试数据管道集成
    """
    print("\n=== 测试数据管道集成 ===")
    
    if not DATA_PIPELINE_AVAILABLE:
        print("⚠️ 数据管道模块不可用，跳过测试")
        return False
    
    try:
        # 创建数据管道实例
        data_pipeline = AdapterManager()
        
        # 测试数据获取
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        print(f"尝试获取数据: {start_date} 到 {end_date}")
        
        # 测试适配器列表
        available_adapters = data_pipeline.list_adapters()
        print(f"✅ 可用适配器: {available_adapters}")
        
        # 测试获取股票适配器
        if 'stock' in available_adapters:
            stock_adapter = data_pipeline.get_adapter('stock')
            print(f"✅ 股票适配器获取成功: {type(stock_adapter).__name__}")
        else:
            print("⚠️ 股票适配器不可用")
        
        # 测试获取基本面适配器
        if 'fundamental' in available_adapters:
            fundamental_adapter = data_pipeline.get_adapter('fundamental')
            print(f"✅ 基本面适配器获取成功: {type(fundamental_adapter).__name__}")
        else:
            print("⚠️ 基本面适配器不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据管道集成测试失败: {e}")
        return False

def run_comprehensive_test():
    """
    运行综合测试
    """
    print("🚀 开始增强网格交易策略综合测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试各个组件
    test_results.append(("网格适合度评价器", test_grid_suitability_evaluator()))
    test_results.append(("对称网格生成器", test_symmetric_grid_generator()))
    test_results.append(("统一策略管理系统", test_unified_strategy_system()))
    if DATA_PIPELINE_AVAILABLE:
        test_results.append(("数据管道集成", test_data_pipeline_integration()))
    else:
        test_results.append(("数据管道集成", False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  - {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！华泰证券网格交易策略增强功能验证成功！")
        print("\n📋 验证的功能:")
        print("  ✅ 网格交易适合度评价")
        print("  ✅ 对称和中心高密度网格生成")
        print("  ✅ 统一策略管理系统集成")
        print("  ✅ 数据管道连接")
        print("\n🚀 系统已准备就绪，可以开始使用增强网格交易策略！")
    else:
        print(f"\n⚠️ 部分测试失败，请检查相关模块")
    
    return passed == total

if __name__ == "__main__":
    # 设置pandas显示选项
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    
    # 运行综合测试
    success = run_comprehensive_test()
    
    if success:
        print("\n" + "=" * 60)
        print("🎯 华泰证券网格交易策略集成完成！")
        print("\n使用示例:")
        print("""
# 获取统一系统
system = get_unified_system()

# 注册策略
system.register_strategy("MyStrategy", MyStrategyClass, StrategyCategory.GRID)

# 创建策略
strategy = system.create_strategy("EnhancedGridTrading", {"param": "value"})

# 运行回测
results = system.run_strategy_backtest("EnhancedGridTrading", "2023-01-01", "2023-12-31")
        """)
    
    sys.exit(0 if success else 1)