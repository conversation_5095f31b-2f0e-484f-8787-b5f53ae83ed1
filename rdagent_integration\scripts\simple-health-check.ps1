# RDAgent简化健康检查脚本
param(
    [string]$ProjectPath = "d:\PycharmProjects\my_rdagent\rdagent_integration"
)

Write-Host "🏥 RDAgent项目健康检查" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Cyan
Write-Host "项目路径: $ProjectPath" -ForegroundColor Gray
Write-Host "检查时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

$healthScore = 0
$maxScore = 100
$issues = @()
$warnings = @()
$recommendations = @()

function Write-HealthLog {
    param([string]$Message, [string]$Type = "INFO")
    $color = switch($Type) {
        "SUCCESS" {"Green"}
        "WARNING" {"Yellow"}
        "ERROR" {"Red"}
        "INFO" {"Cyan"}
        default {"White"}
    }
    Write-Host $Message -ForegroundColor $color
}

# 检查项目路径是否存在
Write-HealthLog "📁 检查项目结构..." "INFO"
if (-not (Test-Path $ProjectPath)) {
    Write-HealthLog "  ❌ 项目路径不存在: $ProjectPath" "ERROR"
    $issues += "项目路径不存在"
    exit 1
} else {
    Write-HealthLog "  ✅ 项目路径存在" "SUCCESS"
    $healthScore += 10
}

# 检查必要目录
$requiredDirs = @("scripts", "logs", "data", "configs", "results")
$missingDirs = @()

foreach ($dir in $requiredDirs) {
    $fullPath = Join-Path $ProjectPath $dir
    if (-not (Test-Path $fullPath)) {
        $missingDirs += $dir
        Write-HealthLog "  ⚠️  缺少目录: $dir" "WARNING"
    } else {
        Write-HealthLog "  ✅ 目录存在: $dir" "SUCCESS"
    }
}

if ($missingDirs.Count -eq 0) {
    $healthScore += 20
} else {
    $warnings += "缺少 $($missingDirs.Count) 个必要目录"
    $recommendations += "创建缺少的目录: $($missingDirs -join ', ')"
    $healthScore += [math]::Max(0, 20 - $missingDirs.Count * 4)
}

# 文件统计
Write-HealthLog "📊 分析文件统计..." "INFO"
try {
    $allFiles = Get-ChildItem $ProjectPath -Recurse -File -ErrorAction SilentlyContinue
    $totalFiles = $allFiles.Count
    $totalSize = ($allFiles | Measure-Object Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
    
    Write-HealthLog "  📈 总文件数: $totalFiles" "INFO"
    Write-HealthLog "  📈 总大小: $totalSizeMB MB" "INFO"
    
    if ($totalFiles -gt 15000) {
        $warnings += "文件数量过多 ($totalFiles)，可能影响性能"
        $recommendations += "考虑清理不必要的文件"
        $healthScore += 5
    } elseif ($totalFiles -gt 10000) {
        $warnings += "文件数量较多 ($totalFiles)，建议定期清理"
        $healthScore += 10
    } else {
        $healthScore += 15
    }
    
} catch {
    Write-HealthLog "  ❌ 文件统计失败: $($_.Exception.Message)" "ERROR"
    $issues += "文件统计失败"
}

# 检查大文件
Write-HealthLog "🔍 检查大文件..." "INFO"
try {
    $largeFiles = Get-ChildItem $ProjectPath -Recurse -File -ErrorAction SilentlyContinue | 
                 Where-Object {$_.Length -gt 50MB}
    
    if ($largeFiles) {
        Write-HealthLog "  ⚠️  发现 $($largeFiles.Count) 个大文件 (>50MB)" "WARNING"
        $largeFiles | ForEach-Object {
            $sizeMB = [math]::Round($_.Length / 1MB, 2)
            Write-HealthLog "    • $($_.Name) - $sizeMB MB" "WARNING"
        }
        $warnings += "发现 $($largeFiles.Count) 个大文件"
        $recommendations += "考虑压缩或移动大文件"
        $healthScore += [math]::Max(0, 10 - $largeFiles.Count * 2)
    } else {
        Write-HealthLog "  ✅ 未发现过大文件" "SUCCESS"
        $healthScore += 10
    }
} catch {
    Write-HealthLog "  ❌ 大文件检查失败" "ERROR"
    $issues += "大文件检查失败"
}

# 检查空目录
Write-HealthLog "📂 检查空目录..." "INFO"
try {
    $emptyDirs = Get-ChildItem $ProjectPath -Recurse -Directory -ErrorAction SilentlyContinue | 
                Where-Object {(Get-ChildItem $_.FullName -ErrorAction SilentlyContinue).Count -eq 0}
    
    if ($emptyDirs) {
        Write-HealthLog "  ⚠️  发现 $($emptyDirs.Count) 个空目录" "WARNING"
        $emptyDirs | Select-Object -First 5 | ForEach-Object {
            $relativePath = $_.FullName.Replace($ProjectPath, "").TrimStart('\')
            Write-HealthLog "    • $relativePath" "WARNING"
        }
        if ($emptyDirs.Count -gt 5) {
            Write-HealthLog "    ... 还有 $($emptyDirs.Count - 5) 个空目录" "WARNING"
        }
        $warnings += "发现 $($emptyDirs.Count) 个空目录"
        $recommendations += "清理不需要的空目录"
        $healthScore += [math]::Max(0, 5 - [math]::Min($emptyDirs.Count, 5))
    } else {
        Write-HealthLog "  ✅ 未发现空目录" "SUCCESS"
        $healthScore += 5
    }
} catch {
    Write-HealthLog "  ❌ 空目录检查失败" "ERROR"
    $issues += "空目录检查失败"
}

# 检查磁盘空间
Write-HealthLog "💾 检查磁盘空间..." "INFO"
try {
    $driveLetter = (Split-Path $ProjectPath -Qualifier).TrimEnd(':')
    $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='${driveLetter}:'" -ErrorAction SilentlyContinue
    
    if ($disk) {
        $freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($disk.Size / 1GB, 2)
        $freePercent = [math]::Round(($disk.FreeSpace / $disk.Size) * 100, 2)
        
        Write-HealthLog "  💽 磁盘 ${driveLetter}: $freeSpaceGB GB / $totalSpaceGB GB ($freePercent%)" "INFO"
        
        if ($freePercent -lt 10) {
            Write-HealthLog "  ❌ 磁盘空间严重不足！" "ERROR"
            $issues += "磁盘空间严重不足 ($freePercent%)"
            $recommendations += "立即清理磁盘空间"
            $healthScore += 0
        } elseif ($freePercent -lt 20) {
            Write-HealthLog "  ⚠️  磁盘空间不足" "WARNING"
            $warnings += "磁盘空间不足 ($freePercent%)"
            $recommendations += "清理磁盘空间"
            $healthScore += 5
        } else {
            Write-HealthLog "  ✅ 磁盘空间充足" "SUCCESS"
            $healthScore += 10
        }
    } else {
        Write-HealthLog "  ⚠️  无法获取磁盘信息" "WARNING"
        $healthScore += 5
    }
} catch {
    Write-HealthLog "  ❌ 磁盘空间检查失败" "ERROR"
    $issues += "磁盘空间检查失败"
}

# 检查系统性能
Write-HealthLog "⚡ 检查系统性能..." "INFO"
try {
    # 检查内存使用
    $memory = Get-WmiObject -Class Win32_OperatingSystem
    $totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
    $freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
    $memoryUsagePercent = [math]::Round((($totalMemoryGB - $freeMemoryGB) / $totalMemoryGB) * 100, 2)
    
    Write-HealthLog "  🧠 内存使用: $memoryUsagePercent% ($freeMemoryGB GB 可用 / $totalMemoryGB GB 总计)" "INFO"
    
    if ($memoryUsagePercent -gt 90) {
        $issues += "内存使用率过高 ($memoryUsagePercent%)"
        $recommendations += "关闭不必要的程序或增加内存"
    } elseif ($memoryUsagePercent -gt 80) {
        $warnings += "内存使用率较高 ($memoryUsagePercent%)"
    }
    
    $healthScore += 10
} catch {
    Write-HealthLog "  ❌ 系统性能检查失败" "ERROR"
    $issues += "系统性能检查失败"
}

# 计算健康评分
$actualScore = [math]::Min($healthScore, $maxScore)
$percentage = [math]::Round(($actualScore / $maxScore) * 100, 1)

$healthLevel = switch ($percentage) {
    {$_ -ge 90} {"优秀"}
    {$_ -ge 80} {"良好"}
    {$_ -ge 70} {"一般"}
    {$_ -ge 60} {"较差"}
    default {"糟糕"}
}

$color = switch ($healthLevel) {
    "优秀" {"Green"}
    "良好" {"Green"}
    "一般" {"Yellow"}
    "较差" {"Red"}
    "糟糕" {"Red"}
    default {"Gray"}
}

# 显示总结
Write-Host "`n" + "=" * 50 -ForegroundColor Cyan
Write-HealthLog "🏥 项目健康检查完成" "SUCCESS"
Write-Host "=" * 50 -ForegroundColor Cyan

Write-Host "`n🎯 整体健康状况: $healthLevel ($percentage%)" -ForegroundColor $color

if ($issues.Count -gt 0) {
    Write-HealthLog "`n❌ 发现的问题 ($($issues.Count)):" "ERROR"
    $issues | ForEach-Object {
        Write-HealthLog "  • $_" "ERROR"
    }
}

if ($warnings.Count -gt 0) {
    Write-HealthLog "`n⚠️  警告 ($($warnings.Count)):" "WARNING"
    $warnings | ForEach-Object {
        Write-HealthLog "  • $_" "WARNING"
    }
}

if ($recommendations.Count -gt 0) {
    Write-HealthLog "`n💡 建议 ($($recommendations.Count)):" "INFO"
    $recommendations | ForEach-Object {
        Write-HealthLog "  • $_" "INFO"
    }
}

# 保存报告
$reportDir = "$ProjectPath\reports"
if (-not (Test-Path $reportDir)) {
    New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
}

$report = @{
    Timestamp = Get-Date
    HealthLevel = $healthLevel
    Percentage = $percentage
    Score = $actualScore
    Issues = $issues
    Warnings = $warnings
    Recommendations = $recommendations
}

$reportFile = "$reportDir\health-check-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
$report | ConvertTo-Json -Depth 3 | Out-File $reportFile -Encoding UTF8

Write-HealthLog "`n📄 详细报告已保存: $reportFile" "INFO"

if ($percentage -lt 70) {
    Write-HealthLog "`n⚠️  建议运行系统优化和清理" "WARNING"
}

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
