#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网格适合度评价器专项调试脚本
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from strategy_autodev.core.enhanced_grid_trading_system import (
        GridTradingSuitabilityEvaluator
    )
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

def test_grid_evaluator_detailed():
    """
    详细测试网格适合度评价器
    """
    print("=== 详细测试网格适合度评价器 ===")
    
    try:
        # 创建评价器实例
        print("1. 创建评价器实例...")
        evaluator = GridTradingSuitabilityEvaluator()
        print("✅ 评价器创建成功")
        
        # 生成测试数据
        print("\n2. 生成测试数据...")
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
        
        # 模拟具有均值回归特征的价格序列
        base_price = 100
        prices = [base_price]
        
        for i in range(1, len(dates)):
            # 添加均值回归特征
            deviation = (prices[-1] - base_price) / base_price
            mean_reversion_force = -0.1 * deviation  # 回归力度
            random_shock = np.random.normal(0, 0.02)  # 随机冲击
            
            price_change = mean_reversion_force + random_shock
            new_price = prices[-1] * (1 + price_change)
            prices.append(max(new_price, 10))  # 防止价格过低
        
        price_series = pd.Series(prices, index=dates)
        print(f"✅ 测试数据生成成功，数据长度: {len(price_series)}")
        print(f"   价格范围: {price_series.min():.2f} - {price_series.max():.2f}")
        
        # 测试评价方法
        print("\n3. 测试适合度评价...")
        suitability = evaluator.evaluate_suitability(price_series)
        print("✅ 适合度评价完成")
        
        # 输出详细结果
        print("\n4. 评价结果:")
        print(f"   是否适合: {suitability.is_suitable}")
        print(f"   波动率评分: {suitability.volatility_score:.3f}")
        print(f"   均值回归评分: {suitability.mean_reversion_score:.3f}")
        print(f"   综合评分: {suitability.overall_score:.3f}")
        print(f"   推荐策略: {suitability.recommendation}")
        
        if hasattr(suitability, 'recommended_grid_type'):
            print(f"   推荐网格类型: {suitability.recommended_grid_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        return False

def test_edge_cases():
    """
    测试边界情况
    """
    print("\n=== 测试边界情况 ===")
    
    evaluator = GridTradingSuitabilityEvaluator()
    
    test_cases = [
        ("空数据", pd.Series([], dtype=float)),
        ("单一价格", pd.Series([100.0] * 10)),
        ("极端波动", pd.Series([100, 200, 50, 150, 25, 175])),
        ("线性趋势", pd.Series(range(100, 200))),
    ]
    
    for case_name, data in test_cases:
        try:
            print(f"\n测试 {case_name}...")
            if len(data) == 0:
                print("⚠️ 跳过空数据测试")
                continue
                
            result = evaluator.evaluate_suitability(data)
            print(f"✅ {case_name} 测试通过")
            print(f"   综合评分: {result.overall_score:.3f}")
            
        except Exception as e:
            print(f"❌ {case_name} 测试失败: {e}")

if __name__ == "__main__":
    print("🔍 开始网格适合度评价器专项调试")
    print("=" * 50)
    
    # 运行详细测试
    success1 = test_grid_evaluator_detailed()
    
    # 运行边界测试
    success2 = True
    try:
        test_edge_cases()
    except Exception as e:
        print(f"边界测试出错: {e}")
        success2 = False
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败")
        sys.exit(1)