﻿import uvicorn
from fastapi import FastAPI
from datetime import datetime
import json

app = FastAPI(title="RDAgent API", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "RDAgent is running", "timestamp": datetime.now().isoformat()}

@app.get("/api/status")
async def get_status():
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/api/health")
async def health_check():
    return {
        "healthy": True,
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    print("Starting RDAgent Web Server...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
