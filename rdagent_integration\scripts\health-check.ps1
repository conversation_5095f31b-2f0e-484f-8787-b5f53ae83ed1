# RDAgent Project Health Check Script
param(
    [string]$ProjectPath = "d:\PycharmProjects\my_rdagent\rdagent_integration"
)

Write-Host "RDAgent Project Health Check" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Cyan
Write-Host "Project Path: $ProjectPath" -ForegroundColor Gray
Write-Host "Check Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

$healthScore = 0
$maxScore = 100
$issues = @()
$warnings = @()
$recommendations = @()

function Write-HealthLog {
    param([string]$Message, [string]$Type = "INFO")
    $color = switch($Type) {
        "SUCCESS" {"Green"}
        "WARNING" {"Yellow"}
        "ERROR" {"Red"}
        "INFO" {"Cyan"}
        default {"White"}
    }
    Write-Host $Message -ForegroundColor $color
}

# Check if project path exists
Write-HealthLog "Checking project structure..." "INFO"
if (-not (Test-Path $ProjectPath)) {
    Write-HealthLog "  ERROR: Project path does not exist: $ProjectPath" "ERROR"
    $issues += "Project path does not exist"
    exit 1
} else {
    Write-HealthLog "  OK: Project path exists" "SUCCESS"
    $healthScore += 10
}

# Check required directories
$requiredDirs = @("scripts", "logs", "data", "configs", "results")
$missingDirs = @()

foreach ($dir in $requiredDirs) {
    $fullPath = Join-Path $ProjectPath $dir
    if (-not (Test-Path $fullPath)) {
        $missingDirs += $dir
        Write-HealthLog "  WARNING: Missing directory: $dir" "WARNING"
    } else {
        Write-HealthLog "  OK: Directory exists: $dir" "SUCCESS"
    }
}

if ($missingDirs.Count -eq 0) {
    $healthScore += 20
} else {
    $warnings += "Missing $($missingDirs.Count) required directories"
    $recommendations += "Create missing directories: $($missingDirs -join ', ')"
    $healthScore += [math]::Max(0, 20 - $missingDirs.Count * 4)
}

# File statistics
Write-HealthLog "Analyzing file statistics..." "INFO"
try {
    $allFiles = Get-ChildItem $ProjectPath -Recurse -File -ErrorAction SilentlyContinue
    $totalFiles = $allFiles.Count
    $totalSize = ($allFiles | Measure-Object Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
    
    Write-HealthLog "  Total files: $totalFiles" "INFO"
    Write-HealthLog "  Total size: $totalSizeMB MB" "INFO"
    
    if ($totalFiles -gt 15000) {
        $warnings += "Too many files ($totalFiles), may affect performance"
        $recommendations += "Consider cleaning unnecessary files"
        $healthScore += 5
    } elseif ($totalFiles -gt 10000) {
        $warnings += "Many files ($totalFiles), recommend regular cleanup"
        $healthScore += 10
    } else {
        $healthScore += 15
    }
    
} catch {
    Write-HealthLog "  ERROR: File statistics failed: $($_.Exception.Message)" "ERROR"
    $issues += "File statistics failed"
}

# Check large files
Write-HealthLog "Checking for large files..." "INFO"
try {
    $largeFiles = Get-ChildItem $ProjectPath -Recurse -File -ErrorAction SilentlyContinue | 
                 Where-Object {$_.Length -gt 50MB}
    
    if ($largeFiles) {
        Write-HealthLog "  WARNING: Found $($largeFiles.Count) large files (>50MB)" "WARNING"
        $largeFiles | ForEach-Object {
            $sizeMB = [math]::Round($_.Length / 1MB, 2)
            Write-HealthLog "    - $($_.Name) - $sizeMB MB" "WARNING"
        }
        $warnings += "Found $($largeFiles.Count) large files"
        $recommendations += "Consider compressing or moving large files"
        $healthScore += [math]::Max(0, 10 - $largeFiles.Count * 2)
    } else {
        Write-HealthLog "  OK: No large files found" "SUCCESS"
        $healthScore += 10
    }
} catch {
    Write-HealthLog "  ERROR: Large file check failed" "ERROR"
    $issues += "Large file check failed"
}

# Check empty directories
Write-HealthLog "Checking for empty directories..." "INFO"
try {
    $emptyDirs = Get-ChildItem $ProjectPath -Recurse -Directory -ErrorAction SilentlyContinue | 
                Where-Object {(Get-ChildItem $_.FullName -ErrorAction SilentlyContinue).Count -eq 0}
    
    if ($emptyDirs) {
        Write-HealthLog "  WARNING: Found $($emptyDirs.Count) empty directories" "WARNING"
        $emptyDirs | Select-Object -First 5 | ForEach-Object {
            $relativePath = $_.FullName.Replace($ProjectPath, "").TrimStart('\')
            Write-HealthLog "    - $relativePath" "WARNING"
        }
        if ($emptyDirs.Count -gt 5) {
            Write-HealthLog "    ... and $($emptyDirs.Count - 5) more empty directories" "WARNING"
        }
        $warnings += "Found $($emptyDirs.Count) empty directories"
        $recommendations += "Clean up unnecessary empty directories"
        $healthScore += [math]::Max(0, 5 - [math]::Min($emptyDirs.Count, 5))
    } else {
        Write-HealthLog "  OK: No empty directories found" "SUCCESS"
        $healthScore += 5
    }
} catch {
    Write-HealthLog "  ERROR: Empty directory check failed" "ERROR"
    $issues += "Empty directory check failed"
}

# Check disk space
Write-HealthLog "Checking disk space..." "INFO"
try {
    $driveLetter = (Split-Path $ProjectPath -Qualifier).TrimEnd(':')
    $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='${driveLetter}:'" -ErrorAction SilentlyContinue
    
    if ($disk) {
        $freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
        $totalSpaceGB = [math]::Round($disk.Size / 1GB, 2)
        $freePercent = [math]::Round(($disk.FreeSpace / $disk.Size) * 100, 2)
        
        Write-HealthLog "  Disk ${driveLetter}: $freeSpaceGB GB / $totalSpaceGB GB ($freePercent%)" "INFO"
        
        if ($freePercent -lt 10) {
            Write-HealthLog "  ERROR: Disk space critically low!" "ERROR"
            $issues += "Disk space critically low ($freePercent%)"
            $recommendations += "Immediately clean disk space"
            $healthScore += 0
        } elseif ($freePercent -lt 20) {
            Write-HealthLog "  WARNING: Disk space low" "WARNING"
            $warnings += "Disk space low ($freePercent%)"
            $recommendations += "Clean disk space"
            $healthScore += 5
        } else {
            Write-HealthLog "  OK: Disk space sufficient" "SUCCESS"
            $healthScore += 10
        }
    } else {
        Write-HealthLog "  WARNING: Cannot get disk information" "WARNING"
        $healthScore += 5
    }
} catch {
    Write-HealthLog "  ERROR: Disk space check failed" "ERROR"
    $issues += "Disk space check failed"
}

# Check system performance
Write-HealthLog "Checking system performance..." "INFO"
try {
    # Check memory usage
    $memory = Get-WmiObject -Class Win32_OperatingSystem
    $totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
    $freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
    $memoryUsagePercent = [math]::Round((($totalMemoryGB - $freeMemoryGB) / $totalMemoryGB) * 100, 2)
    
    Write-HealthLog "  Memory usage: $memoryUsagePercent% ($freeMemoryGB GB free / $totalMemoryGB GB total)" "INFO"
    
    if ($memoryUsagePercent -gt 90) {
        $issues += "Memory usage too high ($memoryUsagePercent%)"
        $recommendations += "Close unnecessary programs or add more memory"
    } elseif ($memoryUsagePercent -gt 80) {
        $warnings += "Memory usage high ($memoryUsagePercent%)"
    }
    
    $healthScore += 10
} catch {
    Write-HealthLog "  ERROR: System performance check failed" "ERROR"
    $issues += "System performance check failed"
}

# Calculate health score
$actualScore = [math]::Min($healthScore, $maxScore)
$percentage = [math]::Round(($actualScore / $maxScore) * 100, 1)

$healthLevel = switch ($percentage) {
    {$_ -ge 90} {"Excellent"}
    {$_ -ge 80} {"Good"}
    {$_ -ge 70} {"Fair"}
    {$_ -ge 60} {"Poor"}
    default {"Critical"}
}

$color = switch ($healthLevel) {
    "Excellent" {"Green"}
    "Good" {"Green"}
    "Fair" {"Yellow"}
    "Poor" {"Red"}
    "Critical" {"Red"}
    default {"Gray"}
}

# Show summary
Write-Host "`n" + "=" * 50 -ForegroundColor Cyan
Write-HealthLog "Project Health Check Complete" "SUCCESS"
Write-Host "=" * 50 -ForegroundColor Cyan

Write-Host "`nOverall Health Status: $healthLevel ($percentage%)" -ForegroundColor $color

if ($issues.Count -gt 0) {
    Write-HealthLog "`nIssues Found ($($issues.Count)):" "ERROR"
    $issues | ForEach-Object {
        Write-HealthLog "  - $_" "ERROR"
    }
}

if ($warnings.Count -gt 0) {
    Write-HealthLog "`nWarnings ($($warnings.Count)):" "WARNING"
    $warnings | ForEach-Object {
        Write-HealthLog "  - $_" "WARNING"
    }
}

if ($recommendations.Count -gt 0) {
    Write-HealthLog "`nRecommendations ($($recommendations.Count)):" "INFO"
    $recommendations | ForEach-Object {
        Write-HealthLog "  - $_" "INFO"
    }
}

# Save report
$reportDir = "$ProjectPath\reports"
if (-not (Test-Path $reportDir)) {
    New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
}

$report = @{
    Timestamp = Get-Date
    HealthLevel = $healthLevel
    Percentage = $percentage
    Score = $actualScore
    Issues = $issues
    Warnings = $warnings
    Recommendations = $recommendations
}

$reportFile = "$reportDir\health-check-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
$report | ConvertTo-Json -Depth 3 | Out-File $reportFile -Encoding UTF8

Write-HealthLog "`nDetailed report saved: $reportFile" "INFO"

if ($percentage -lt 70) {
    Write-HealthLog "`nRecommend running system optimization and cleanup" "WARNING"
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
