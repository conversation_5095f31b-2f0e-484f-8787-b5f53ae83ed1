# RDAgent可视化控制面板
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

$ProjectRoot = "d:\PycharmProjects\my_rdagent\rdagent_integration"

# 创建主窗体
$form = New-Object System.Windows.Forms.Form
$form.Text = "RDAgent 量化交易平台控制面板"
$form.Size = New-Object System.Drawing.Size(800, 600)
$form.StartPosition = "CenterScreen"
$form.FormBorderStyle = "FixedDialog"
$form.MaximizeBox = $false
$form.BackColor = [System.Drawing.Color]::FromArgb(240, 240, 240)

# 创建字体
$titleFont = New-Object System.Drawing.Font("Microsoft YaHei", 14, [System.Drawing.FontStyle]::Bold)
$buttonFont = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$statusFont = New-Object System.Drawing.Font("Microsoft YaHei", 12, [System.Drawing.FontStyle]::Bold)

# 标题标签
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Location = New-Object System.Drawing.Point(20, 20)
$titleLabel.Size = New-Object System.Drawing.Size(750, 40)
$titleLabel.Text = "🚀 RDAgent 量化交易平台控制面板"
$titleLabel.Font = $titleFont
$titleLabel.ForeColor = [System.Drawing.Color]::FromArgb(44, 62, 80)
$titleLabel.TextAlign = "MiddleCenter"
$form.Controls.Add($titleLabel)

# 状态显示区域
$statusPanel = New-Object System.Windows.Forms.Panel
$statusPanel.Location = New-Object System.Drawing.Point(20, 70)
$statusPanel.Size = New-Object System.Drawing.Size(750, 80)
$statusPanel.BackColor = [System.Drawing.Color]::White
$statusPanel.BorderStyle = "FixedSingle"
$form.Controls.Add($statusPanel)

# 系统状态标签
$statusLabel = New-Object System.Windows.Forms.Label
$statusLabel.Location = New-Object System.Drawing.Point(20, 15)
$statusLabel.Size = New-Object System.Drawing.Size(400, 25)
$statusLabel.Text = "系统状态: 检查中..."
$statusLabel.Font = $statusFont
$statusLabel.ForeColor = [System.Drawing.Color]::FromArgb(52, 152, 219)
$statusPanel.Controls.Add($statusLabel)

# 进程信息标签
$processLabel = New-Object System.Windows.Forms.Label
$processLabel.Location = New-Object System.Drawing.Point(20, 45)
$processLabel.Size = New-Object System.Drawing.Size(400, 20)
$processLabel.Text = "运行进程: 0"
$processLabel.Font = $buttonFont
$processLabel.ForeColor = [System.Drawing.Color]::FromArgb(127, 140, 141)
$statusPanel.Controls.Add($processLabel)

# Web状态标签
$webStatusLabel = New-Object System.Windows.Forms.Label
$webStatusLabel.Location = New-Object System.Drawing.Point(450, 15)
$webStatusLabel.Size = New-Object System.Drawing.Size(280, 25)
$webStatusLabel.Text = "Web服务: 未知"
$webStatusLabel.Font = $buttonFont
$webStatusLabel.ForeColor = [System.Drawing.Color]::FromArgb(127, 140, 141)
$statusPanel.Controls.Add($webStatusLabel)

# 最后更新时间标签
$updateTimeLabel = New-Object System.Windows.Forms.Label
$updateTimeLabel.Location = New-Object System.Drawing.Point(450, 45)
$updateTimeLabel.Size = New-Object System.Drawing.Size(280, 20)
$updateTimeLabel.Text = "更新时间: --"
$updateTimeLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$updateTimeLabel.ForeColor = [System.Drawing.Color]::FromArgb(149, 165, 166)
$statusPanel.Controls.Add($updateTimeLabel)

# 按钮区域
$buttonPanel = New-Object System.Windows.Forms.Panel
$buttonPanel.Location = New-Object System.Drawing.Point(20, 170)
$buttonPanel.Size = New-Object System.Drawing.Size(750, 120)
$form.Controls.Add($buttonPanel)

# 创建按钮的函数
function Create-Button {
    param($text, $x, $y, $width, $height, $backColor, $action)
    
    $button = New-Object System.Windows.Forms.Button
    $button.Location = New-Object System.Drawing.Point($x, $y)
    $button.Size = New-Object System.Drawing.Size($width, $height)
    $button.Text = $text
    $button.Font = $buttonFont
    $button.BackColor = $backColor
    $button.ForeColor = [System.Drawing.Color]::White
    $button.FlatStyle = "Flat"
    $button.FlatAppearance.BorderSize = 0
    $button.Cursor = "Hand"
    $button.Add_Click($action)
    
    return $button
}

# 第一行按钮
$startButton = Create-Button "🚀 启动系统" 20 20 140 40 ([System.Drawing.Color]::FromArgb(46, 204, 113)) {
    $statusLabel.Text = "系统状态: 正在启动..."
    $statusLabel.ForeColor = [System.Drawing.Color]::FromArgb(230, 126, 34)
    $form.Refresh()
    
    try {
        Start-Process -FilePath "powershell" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", "$ProjectRoot\scripts\project-manager.ps1", "-Action", "start" -WindowStyle Hidden -Wait
        Start-Sleep 3
        Update-Status
    } catch {
        [System.Windows.Forms.MessageBox]::Show("启动失败: $($_.Exception.Message)", "错误", "OK", "Error")
    }
}
$buttonPanel.Controls.Add($startButton)

$stopButton = Create-Button "🛑 停止系统" 180 20 140 40 ([System.Drawing.Color]::FromArgb(231, 76, 60)) {
    $statusLabel.Text = "系统状态: 正在停止..."
    $statusLabel.ForeColor = [System.Drawing.Color]::FromArgb(230, 126, 34)
    $form.Refresh()
    
    try {
        Start-Process -FilePath "powershell" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", "$ProjectRoot\scripts\project-manager.ps1", "-Action", "stop" -WindowStyle Hidden -Wait
        Start-Sleep 2
        Update-Status
    } catch {
        [System.Windows.Forms.MessageBox]::Show("停止失败: $($_.Exception.Message)", "错误", "OK", "Error")
    }
}
$buttonPanel.Controls.Add($stopButton)

$healthButton = Create-Button "🏥 健康检查" 340 20 140 40 ([System.Drawing.Color]::FromArgb(52, 152, 219)) {
    try {
        Start-Process -FilePath "powershell" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", "$ProjectRoot\scripts\project-health-check.ps1", "-Detailed"
    } catch {
        [System.Windows.Forms.MessageBox]::Show("健康检查启动失败: $($_.Exception.Message)", "错误", "OK", "Error")
    }
}
$buttonPanel.Controls.Add($healthButton)

$backupButton = Create-Button "💾 备份项目" 500 20 140 40 ([System.Drawing.Color]::FromArgb(155, 89, 182)) {
    $result = [System.Windows.Forms.MessageBox]::Show("确定要备份项目吗？这可能需要几分钟时间。", "确认备份", "YesNo", "Question")
    if ($result -eq "Yes") {
        try {
            Start-Process -FilePath "powershell" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", "$ProjectRoot\scripts\project-manager.ps1", "-Action", "backup"
        } catch {
            [System.Windows.Forms.MessageBox]::Show("备份启动失败: $($_.Exception.Message)", "错误", "OK", "Error")
        }
    }
}
$buttonPanel.Controls.Add($backupButton)

# 第二行按钮
$openProjectButton = Create-Button "📝 打开项目" 20 70 140 40 ([System.Drawing.Color]::FromArgb(41, 128, 185)) {
    try {
        if (Test-Path "$ProjectRoot\..\rdagent.code-workspace") {
            Start-Process -FilePath "code" -ArgumentList "$ProjectRoot\..\rdagent.code-workspace"
        } else {
            Start-Process -FilePath "code" -ArgumentList $ProjectRoot
        }
    } catch {
        [System.Windows.Forms.MessageBox]::Show("无法打开VS Code: $($_.Exception.Message)", "错误", "OK", "Error")
    }
}
$buttonPanel.Controls.Add($openProjectButton)

$openWebButton = Create-Button "🌐 Web界面" 180 70 140 40 ([System.Drawing.Color]::FromArgb(26, 188, 156)) {
    try {
        Start-Process "http://localhost:8000"
    } catch {
        [System.Windows.Forms.MessageBox]::Show("无法打开Web界面，请确保系统已启动", "错误", "OK", "Error")
    }
}
$buttonPanel.Controls.Add($openWebButton)

$cleanButton = Create-Button "🧹 系统清理" 340 70 140 40 ([System.Drawing.Color]::FromArgb(243, 156, 18)) {
    $result = [System.Windows.Forms.MessageBox]::Show("确定要清理系统数据吗？这将删除临时文件和旧日志。", "确认清理", "YesNo", "Question")
    if ($result -eq "Yes") {
        try {
            Start-Process -FilePath "powershell" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", "$ProjectRoot\scripts\project-manager.ps1", "-Action", "clean"
        } catch {
            [System.Windows.Forms.MessageBox]::Show("清理启动失败: $($_.Exception.Message)", "错误", "OK", "Error")
        }
    }
}
$buttonPanel.Controls.Add($cleanButton)

$settingsButton = Create-Button "⚙️ 设置" 500 70 140 40 ([System.Drawing.Color]::FromArgb(127, 140, 141)) {
    [System.Windows.Forms.MessageBox]::Show("设置功能开发中...", "信息", "OK", "Information")
}
$buttonPanel.Controls.Add($settingsButton)

# 日志显示区域
$logLabel = New-Object System.Windows.Forms.Label
$logLabel.Location = New-Object System.Drawing.Point(20, 310)
$logLabel.Size = New-Object System.Drawing.Size(200, 20)
$logLabel.Text = "📝 系统日志"
$logLabel.Font = $buttonFont
$logLabel.ForeColor = [System.Drawing.Color]::FromArgb(44, 62, 80)
$form.Controls.Add($logLabel)

$logTextBox = New-Object System.Windows.Forms.TextBox
$logTextBox.Location = New-Object System.Drawing.Point(20, 340)
$logTextBox.Size = New-Object System.Drawing.Size(750, 150)
$logTextBox.Multiline = $true
$logTextBox.ScrollBars = "Vertical"
$logTextBox.ReadOnly = $true
$logTextBox.Font = New-Object System.Drawing.Font("Consolas", 9)
$logTextBox.BackColor = [System.Drawing.Color]::FromArgb(44, 62, 80)
$logTextBox.ForeColor = [System.Drawing.Color]::FromArgb(236, 240, 241)
$form.Controls.Add($logTextBox)

# 底部按钮
$refreshButton = New-Object System.Windows.Forms.Button
$refreshButton.Location = New-Object System.Drawing.Point(20, 510)
$refreshButton.Size = New-Object System.Drawing.Size(100, 30)
$refreshButton.Text = "🔄 刷新"
$refreshButton.Font = $buttonFont
$refreshButton.BackColor = [System.Drawing.Color]::FromArgb(52, 152, 219)
$refreshButton.ForeColor = [System.Drawing.Color]::White
$refreshButton.FlatStyle = "Flat"
$refreshButton.FlatAppearance.BorderSize = 0
$refreshButton.Add_Click({
    Update-Status
    Update-Logs
})
$form.Controls.Add($refreshButton)

$exitButton = New-Object System.Windows.Forms.Button
$exitButton.Location = New-Object System.Drawing.Point(670, 510)
$exitButton.Size = New-Object System.Drawing.Size(100, 30)
$exitButton.Text = "❌ 退出"
$exitButton.Font = $buttonFont
$exitButton.BackColor = [System.Drawing.Color]::FromArgb(231, 76, 60)
$exitButton.ForeColor = [System.Drawing.Color]::White
$exitButton.FlatStyle = "Flat"
$exitButton.FlatAppearance.BorderSize = 0
$exitButton.Add_Click({
    $form.Close()
})
$form.Controls.Add($exitButton)

# 自动刷新定时器
$timer = New-Object System.Windows.Forms.Timer
$timer.Interval = 30000  # 30秒
$timer.Add_Tick({
    Update-Status
})
$timer.Start()

# 更新状态函数
function Update-Status {
    try {
        # 检查Web服务
        $webStatus = Test-NetConnection -ComputerName localhost -Port 8000 -InformationLevel Quiet -WarningAction SilentlyContinue
        
        # 检查Python进程
        $pythonProcesses = Get-Process python -ErrorAction SilentlyContinue | Where-Object {
            $_.Path -like "*rdagent*" -or 
            (Get-NetTCPConnection -OwningProcess $_.Id -ErrorAction SilentlyContinue | Where-Object {$_.LocalPort -eq 8000})
        }
        
        # 更新状态显示
        if ($webStatus -and $pythonProcesses) {
            $statusLabel.Text = "系统状态: 🟢 运行中"
            $statusLabel.ForeColor = [System.Drawing.Color]::FromArgb(46, 204, 113)
            $webStatusLabel.Text = "Web服务: ✅ 正常 (端口8000)"
        } elseif ($pythonProcesses) {
            $statusLabel.Text = "系统状态: 🟡 部分运行"
            $statusLabel.ForeColor = [System.Drawing.Color]::FromArgb(230, 126, 34)
            $webStatusLabel.Text = "Web服务: ❌ 异常"
        } else {
            $statusLabel.Text = "系统状态: 🔴 已停止"
            $statusLabel.ForeColor = [System.Drawing.Color]::FromArgb(231, 76, 60)
            $webStatusLabel.Text = "Web服务: ❌ 未运行"
        }
        
        $processLabel.Text = "运行进程: $($pythonProcesses.Count)"
        $updateTimeLabel.Text = "更新时间: $(Get-Date -Format 'HH:mm:ss')"
        
    } catch {
        $statusLabel.Text = "系统状态: ❌ 检查失败"
        $statusLabel.ForeColor = [System.Drawing.Color]::FromArgb(231, 76, 60)
        $webStatusLabel.Text = "Web服务: ❌ 未知"
        $processLabel.Text = "运行进程: 未知"
    }
}

# 更新日志函数
function Update-Logs {
    try {
        $logContent = ""
        $logFiles = @(
            "$ProjectRoot\logs\project-manager.log",
            "$ProjectRoot\logs\web_server.log",
            "$ProjectRoot\logs\system.log"
        )
        
        foreach ($logFile in $logFiles) {
            if (Test-Path $logFile) {
                $recentLogs = Get-Content $logFile -Tail 5 -ErrorAction SilentlyContinue
                if ($recentLogs) {
                    $logContent += "=== $(Split-Path $logFile -Leaf) ===`r`n"
                    $logContent += ($recentLogs -join "`r`n") + "`r`n`r`n"
                }
            }
        }
        
        if (-not $logContent) {
            $logContent = "暂无日志信息`r`n`r`n系统提示:`r`n- 启动系统后将显示运行日志`r`n- 日志文件位于 logs\ 目录下`r`n- 可以手动查看详细日志文件"
        }
        
        $logTextBox.Text = $logContent
        $logTextBox.SelectionStart = $logTextBox.Text.Length
        $logTextBox.ScrollToCaret()
        
    } catch {
        $logTextBox.Text = "日志读取失败: $($_.Exception.Message)"
    }
}

# 窗体关闭事件
$form.Add_FormClosing({
    $timer.Stop()
    $timer.Dispose()
})

# 初始化
Update-Status
Update-Logs

# 显示窗体
Write-Host "🎛️  启动RDAgent控制面板..." -ForegroundColor Green
Write-Host "💡 提示: 控制面板将自动每30秒刷新状态" -ForegroundColor Cyan

$form.ShowDialog() | Out-Null
