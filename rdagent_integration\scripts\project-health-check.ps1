# RDAgent项目健康检查脚本
param(
    [string]$ProjectPath = "d:\PycharmProjects\my_rdagent\rdagent_integration",
    [switch]$Detailed = $false,
    [switch]$AutoFix = $false
)

Write-Host "🏥 RDAgent项目健康检查" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Cyan
Write-Host "项目路径: $ProjectPath" -ForegroundColor Gray
Write-Host "检查时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

$healthReport = @{
    Timestamp = Get-Date
    ProjectPath = $ProjectPath
    OverallHealth = "Unknown"
    Score = 0
    Issues = @()
    Warnings = @()
    Recommendations = @()
    Statistics = @{}
    FixedIssues = @()
}

function Write-HealthLog {
    param([string]$Message, [string]$Type = "INFO")
    $color = switch($Type) {
        "SUCCESS" {"Green"}
        "WARNING" {"Yellow"}
        "ERROR" {"Red"}
        "INFO" {"Cyan"}
        default {"White"}
    }
    Write-Host $Message -ForegroundColor $color
}

function Test-ProjectStructure {
    Write-HealthLog "📁 检查项目结构..." "INFO"
    
    $requiredDirs = @(
        "scripts", "logs", "data", "configs", "results", "backups"
    )
    
    $missingDirs = @()
    foreach ($dir in $requiredDirs) {
        $fullPath = Join-Path $ProjectPath $dir
        if (-not (Test-Path $fullPath)) {
            $missingDirs += $dir
            $healthReport.Issues += "缺少必要目录: $dir"
            
            if ($AutoFix) {
                New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
                $healthReport.FixedIssues += "创建目录: $dir"
                Write-HealthLog "  ✅ 自动创建目录: $dir" "SUCCESS"
            }
        }
    }
    
    if ($missingDirs.Count -eq 0) {
        Write-HealthLog "  ✅ 项目结构完整" "SUCCESS"
        $healthReport.Score += 15
    } else {
        Write-HealthLog "  ⚠️  缺少 $($missingDirs.Count) 个必要目录" "WARNING"
        $healthReport.Score += [math]::Max(0, 15 - $missingDirs.Count * 3)
    }
}

function Test-FileStatistics {
    Write-HealthLog "📊 分析文件统计..." "INFO"
    
    try {
        $allFiles = Get-ChildItem $ProjectPath -Recurse -File -ErrorAction SilentlyContinue
        $totalFiles = $allFiles.Count
        $totalSize = ($allFiles | Measure-Object Length -Sum).Sum
        $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
        
        $healthReport.Statistics["TotalFiles"] = $totalFiles
        $healthReport.Statistics["TotalSizeMB"] = $totalSizeMB
        
        # 文件类型分析
        $fileTypes = $allFiles | Group-Object Extension | Sort-Object Count -Descending
        $healthReport.Statistics["FileTypes"] = @{}
        
        Write-HealthLog "  📈 文件统计:" "INFO"
        Write-HealthLog "    总文件数: $totalFiles" "INFO"
        Write-HealthLog "    总大小: $totalSizeMB MB" "INFO"
        
        if ($Detailed) {
            Write-HealthLog "  📋 文件类型分布:" "INFO"
            $fileTypes | Select-Object -First 10 | ForEach-Object {
                $ext = if($_.Name) {$_.Name} else {"无扩展名"}
                $healthReport.Statistics["FileTypes"][$ext] = $_.Count
                Write-HealthLog "    $ext : $($_.Count) 个文件" "INFO"
            }
        }
        
        # 检查文件数量是否合理
        if ($totalFiles -gt 15000) {
            $healthReport.Warnings += "文件数量过多 ($totalFiles)，可能影响性能"
            $healthReport.Recommendations += "考虑清理不必要的文件或优化项目结构"
        } elseif ($totalFiles -gt 10000) {
            $healthReport.Warnings += "文件数量较多 ($totalFiles)，建议定期清理"
        }
        
        $healthReport.Score += 10
        
    } catch {
        Write-HealthLog "  ❌ 文件统计分析失败: $($_.Exception.Message)" "ERROR"
        $healthReport.Issues += "文件统计分析失败"
    }
}

function Test-LargeFiles {
    Write-HealthLog "🔍 检查大文件..." "INFO"
    
    try {
        $largeFiles = Get-ChildItem $ProjectPath -Recurse -File -ErrorAction SilentlyContinue | 
                     Where-Object {$_.Length -gt 50MB}
        
        if ($largeFiles) {
            Write-HealthLog "  ⚠️  发现 $($largeFiles.Count) 个大文件 (>50MB):" "WARNING"
            $largeFiles | ForEach-Object {
                $sizeMB = [math]::Round($_.Length / 1MB, 2)
                Write-HealthLog "    • $($_.Name) - $sizeMB MB" "WARNING"
                $healthReport.Warnings += "大文件: $($_.Name) ($sizeMB MB)"
            }
            $healthReport.Recommendations += "考虑压缩或移动大文件到外部存储"
            $healthReport.Score += [math]::Max(0, 10 - $largeFiles.Count * 2)
        } else {
            Write-HealthLog "  ✅ 未发现过大文件" "SUCCESS"
            $healthReport.Score += 10
        }
        
    } catch {
        Write-HealthLog "  ❌ 大文件检查失败" "ERROR"
        $healthReport.Issues += "大文件检查失败"
    }
}

function Test-DuplicateFiles {
    Write-HealthLog "🔄 检查重复文件..." "INFO"
    
    try {
        $files = Get-ChildItem $ProjectPath -Recurse -File -ErrorAction SilentlyContinue | 
                Where-Object {$_.Length -gt 1KB -and $_.Length -lt 10MB}  # 只检查中等大小的文件
        
        Write-HealthLog "  🔍 分析 $($files.Count) 个文件的哈希值..." "INFO"
        
        $duplicateGroups = $files | 
                          Get-FileHash -Algorithm MD5 -ErrorAction SilentlyContinue | 
                          Group-Object Hash | 
                          Where-Object {$_.Count -gt 1}
        
        if ($duplicateGroups) {
            $duplicateCount = ($duplicateGroups | Measure-Object Count -Sum).Sum - $duplicateGroups.Count
            Write-HealthLog "  ⚠️  发现 $duplicateCount 个重复文件:" "WARNING"
            
            $duplicateGroups | ForEach-Object {
                Write-HealthLog "    重复组 (哈希: $($_.Name.Substring(0,8))...):" "WARNING"
                $_.Group | ForEach-Object {
                    $relativePath = $_.Path.Replace($ProjectPath, "").TrimStart('\')
                    Write-HealthLog "      • $relativePath" "WARNING"
                }
            }
            
            $healthReport.Warnings += "发现 $duplicateCount 个重复文件"
            $healthReport.Recommendations += "清理重复文件以节省空间"
            $healthReport.Score += [math]::Max(0, 10 - [math]::Min($duplicateCount, 10))
        } else {
            Write-HealthLog "  ✅ 未发现重复文件" "SUCCESS"
            $healthReport.Score += 10
        }
        
    } catch {
        Write-HealthLog "  ❌ 重复文件检查失败: $($_.Exception.Message)" "ERROR"
        $healthReport.Issues += "重复文件检查失败"
    }
}

function Test-EmptyDirectories {
    Write-HealthLog "📂 检查空目录..." "INFO"
    
    try {
        $emptyDirs = Get-ChildItem $ProjectPath -Recurse -Directory -ErrorAction SilentlyContinue | 
                    Where-Object {(Get-ChildItem $_.FullName -ErrorAction SilentlyContinue).Count -eq 0}
        
        if ($emptyDirs) {
            Write-HealthLog "  ⚠️  发现 $($emptyDirs.Count) 个空目录:" "WARNING"
            $emptyDirs | ForEach-Object {
                $relativePath = $_.FullName.Replace($ProjectPath, "").TrimStart('\')
                Write-HealthLog "    • $relativePath" "WARNING"
                
                if ($AutoFix) {
                    Remove-Item $_.FullName -Force -ErrorAction SilentlyContinue
                    $healthReport.FixedIssues += "删除空目录: $relativePath"
                }
            }
            
            if ($AutoFix) {
                Write-HealthLog "  ✅ 已自动清理空目录" "SUCCESS"
            } else {
                $healthReport.Warnings += "发现 $($emptyDirs.Count) 个空目录"
                $healthReport.Recommendations += "清理不需要的空目录"
            }
            $healthReport.Score += [math]::Max(0, 5 - [math]::Min($emptyDirs.Count, 5))
        } else {
            Write-HealthLog "  ✅ 未发现空目录" "SUCCESS"
            $healthReport.Score += 5
        }
        
    } catch {
        Write-HealthLog "  ❌ 空目录检查失败" "ERROR"
        $healthReport.Issues += "空目录检查失败"
    }
}

function Test-PythonSyntax {
    Write-HealthLog "🐍 检查Python语法..." "INFO"
    
    try {
        $pythonFiles = Get-ChildItem $ProjectPath -Filter "*.py" -Recurse -ErrorAction SilentlyContinue
        $syntaxErrors = @()
        
        if ($pythonFiles.Count -eq 0) {
            Write-HealthLog "  ℹ️  未找到Python文件" "INFO"
            $healthReport.Score += 10
            return
        }
        
        Write-HealthLog "  🔍 检查 $($pythonFiles.Count) 个Python文件..." "INFO"
        
        foreach ($file in $pythonFiles) {
            try {
                $result = & python -m py_compile $file.FullName 2>&1
                if ($LASTEXITCODE -ne 0) {
                    $relativePath = $file.FullName.Replace($ProjectPath, "").TrimStart('\')
                    $syntaxErrors += $relativePath
                    Write-HealthLog "    ❌ 语法错误: $relativePath" "ERROR"
                }
            } catch {
                # Python可能未安装，跳过语法检查
                Write-HealthLog "  ⚠️  Python未安装或不可用，跳过语法检查" "WARNING"
                $healthReport.Score += 5
                return
            }
        }
        
        if ($syntaxErrors.Count -eq 0) {
            Write-HealthLog "  ✅ 所有Python文件语法正确" "SUCCESS"
            $healthReport.Score += 15
        } else {
            Write-HealthLog "  ❌ 发现 $($syntaxErrors.Count) 个语法错误" "ERROR"
            $healthReport.Issues += "Python语法错误: $($syntaxErrors.Count) 个文件"
            $healthReport.Score += [math]::Max(0, 15 - $syntaxErrors.Count * 3)
        }
        
    } catch {
        Write-HealthLog "  ❌ Python语法检查失败" "ERROR"
        $healthReport.Issues += "Python语法检查失败"
    }
}

function Test-DiskSpace {
    Write-HealthLog "💾 检查磁盘空间..." "INFO"
    
    try {
        $driveLetter = (Split-Path $ProjectPath -Qualifier).TrimEnd(':')
        $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='${driveLetter}:'" -ErrorAction SilentlyContinue
        
        if ($disk) {
            $freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
            $totalSpaceGB = [math]::Round($disk.Size / 1GB, 2)
            $freePercent = [math]::Round(($disk.FreeSpace / $disk.Size) * 100, 2)
            
            $healthReport.Statistics["DiskFreeGB"] = $freeSpaceGB
            $healthReport.Statistics["DiskTotalGB"] = $totalSpaceGB
            $healthReport.Statistics["DiskFreePercent"] = $freePercent
            
            Write-HealthLog "  💽 磁盘 ${driveLetter}: $freeSpaceGB GB / $totalSpaceGB GB ($freePercent%)" "INFO"
            
            if ($freePercent -lt 10) {
                Write-HealthLog "  ❌ 磁盘空间严重不足！" "ERROR"
                $healthReport.Issues += "磁盘空间严重不足 ($freePercent%)"
                $healthReport.Recommendations += "立即清理磁盘空间或扩展存储"
                $healthReport.Score += 0
            } elseif ($freePercent -lt 20) {
                Write-HealthLog "  ⚠️  磁盘空间不足" "WARNING"
                $healthReport.Warnings += "磁盘空间不足 ($freePercent%)"
                $healthReport.Recommendations += "清理磁盘空间或扩展存储"
                $healthReport.Score += 5
            } else {
                Write-HealthLog "  ✅ 磁盘空间充足" "SUCCESS"
                $healthReport.Score += 10
            }
        } else {
            Write-HealthLog "  ⚠️  无法获取磁盘信息" "WARNING"
            $healthReport.Score += 5
        }
        
    } catch {
        Write-HealthLog "  ❌ 磁盘空间检查失败" "ERROR"
        $healthReport.Issues += "磁盘空间检查失败"
    }
}

function Test-SystemPerformance {
    Write-HealthLog "⚡ 检查系统性能..." "INFO"
    
    try {
        # 检查内存使用
        $memory = Get-WmiObject -Class Win32_OperatingSystem
        $totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
        $freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
        $memoryUsagePercent = [math]::Round((($totalMemoryGB - $freeMemoryGB) / $totalMemoryGB) * 100, 2)
        
        $healthReport.Statistics["TotalMemoryGB"] = $totalMemoryGB
        $healthReport.Statistics["FreeMemoryGB"] = $freeMemoryGB
        $healthReport.Statistics["MemoryUsagePercent"] = $memoryUsagePercent
        
        Write-HealthLog "  🧠 内存使用: $memoryUsagePercent% ($freeMemoryGB GB 可用 / $totalMemoryGB GB 总计)" "INFO"
        
        if ($memoryUsagePercent -gt 90) {
            $healthReport.Issues += "内存使用率过高 ($memoryUsagePercent%)"
            $healthReport.Recommendations += "关闭不必要的程序或增加内存"
        } elseif ($memoryUsagePercent -gt 80) {
            $healthReport.Warnings += "内存使用率较高 ($memoryUsagePercent%)"
        }
        
        # 检查CPU使用率（简单采样）
        $cpu = Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average
        $avgCpuUsage = [math]::Round($cpu.Average, 2)
        
        $healthReport.Statistics["CPUUsagePercent"] = $avgCpuUsage
        Write-HealthLog "  🖥️  CPU使用率: $avgCpuUsage%" "INFO"
        
        if ($avgCpuUsage -gt 80) {
            $healthReport.Warnings += "CPU使用率较高 ($avgCpuUsage%)"
        }
        
        $healthReport.Score += 10
        
    } catch {
        Write-HealthLog "  ❌ 系统性能检查失败" "ERROR"
        $healthReport.Issues += "系统性能检查失败"
    }
}

function Get-HealthScore {
    $maxScore = 100
    $actualScore = [math]::Min($healthReport.Score, $maxScore)
    $percentage = [math]::Round(($actualScore / $maxScore) * 100, 1)
    
    $healthLevel = switch ($percentage) {
        {$_ -ge 90} {"优秀"}
        {$_ -ge 80} {"良好"}
        {$_ -ge 70} {"一般"}
        {$_ -ge 60} {"较差"}
        default {"糟糕"}
    }
    
    $healthReport.OverallHealth = $healthLevel
    $healthReport.HealthPercentage = $percentage
    
    return @{
        Score = $actualScore
        Percentage = $percentage
        Level = $healthLevel
    }
}

function Show-HealthSummary {
    Write-Host "`n" + "=" * 50 -ForegroundColor Cyan
    Write-HealthLog "🏥 项目健康检查完成" "SUCCESS"
    Write-Host "=" * 50 -ForegroundColor Cyan
    
    $healthScore = Get-HealthScore
    
    $color = switch ($healthScore.Level) {
        "优秀" {"Green"}
        "良好" {"Green"}
        "一般" {"Yellow"}
        "较差" {"Red"}
        "糟糕" {"Red"}
        default {"Gray"}
    }
    
    Write-Host "`n🎯 整体健康状况: $($healthScore.Level) ($($healthScore.Percentage)%)" -ForegroundColor $color
    
    if ($healthReport.Issues.Count -gt 0) {
        Write-HealthLog "`n❌ 发现的问题 ($($healthReport.Issues.Count)):" "ERROR"
        $healthReport.Issues | ForEach-Object {
            Write-HealthLog "  • $_" "ERROR"
        }
    }
    
    if ($healthReport.Warnings.Count -gt 0) {
        Write-HealthLog "`n⚠️  警告 ($($healthReport.Warnings.Count)):" "WARNING"
        $healthReport.Warnings | ForEach-Object {
            Write-HealthLog "  • $_" "WARNING"
        }
    }
    
    if ($healthReport.Recommendations.Count -gt 0) {
        Write-HealthLog "`n💡 建议 ($($healthReport.Recommendations.Count)):" "INFO"
        $healthReport.Recommendations | ForEach-Object {
            Write-HealthLog "  • $_" "INFO"
        }
    }
    
    if ($AutoFix -and $healthReport.FixedIssues.Count -gt 0) {
        Write-HealthLog "`n🔧 自动修复 ($($healthReport.FixedIssues.Count)):" "SUCCESS"
        $healthReport.FixedIssues | ForEach-Object {
            Write-HealthLog "  • $_" "SUCCESS"
        }
    }
    
    # 保存详细报告
    $reportDir = "$ProjectPath\reports"
    if (-not (Test-Path $reportDir)) {
        New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
    }
    
    $reportFile = "$reportDir\health-check-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    $healthReport | ConvertTo-Json -Depth 4 | Out-File $reportFile -Encoding UTF8
    
    Write-HealthLog "`n📄 详细报告已保存: $reportFile" "INFO"
    
    if ($healthScore.Percentage -lt 70) {
        Write-HealthLog "`n⚠️  建议运行自动修复: .\project-health-check.ps1 -AutoFix" "WARNING"
    }
}

# 执行健康检查
try {
    if (-not (Test-Path $ProjectPath)) {
        Write-HealthLog "❌ 项目路径不存在: $ProjectPath" "ERROR"
        exit 1
    }
    
    Test-ProjectStructure
    Test-FileStatistics
    Test-LargeFiles
    Test-DuplicateFiles
    Test-EmptyDirectories
    Test-PythonSyntax
    Test-DiskSpace
    Test-SystemPerformance
    
    Show-HealthSummary
    
} catch {
    Write-HealthLog "❌ 健康检查过程中发生错误: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
