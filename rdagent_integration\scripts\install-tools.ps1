# RDAgent工具自动安装脚本
# 执行策略：Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

Write-Host "🛠️  开始安装RDAgent开发工具链..." -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "⚠️  建议以管理员身份运行以获得最佳安装体验" -ForegroundColor Yellow
}

# 检查并安装Chocolatey
Write-Host "📦 检查包管理器..." -ForegroundColor Cyan
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "安装Chocolatey包管理器..." -ForegroundColor Yellow
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Write-Host "✅ Chocolatey安装成功" -ForegroundColor Green
    } catch {
        Write-Host "❌ Chocolatey安装失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动安装Chocolatey后重新运行此脚本" -ForegroundColor Yellow
        exit 1
    }
} else {
    Write-Host "✅ Chocolatey已安装" -ForegroundColor Green
}

# 核心工具列表
$coreTools = @(
    @{Name="everything"; Description="超快文件搜索工具"},
    @{Name="vscode"; Description="Visual Studio Code编辑器"},
    @{Name="git"; Description="Git版本控制系统"},
    @{Name="github-desktop"; Description="GitHub桌面客户端"},
    @{Name="powershell-core"; Description="PowerShell 7"},
    @{Name="python"; Description="Python编程语言"},
    @{Name="nodejs"; Description="Node.js运行时"}
)

# 可选工具列表
$optionalTools = @(
    @{Name="postman"; Description="API测试工具"},
    @{Name="ccleaner"; Description="系统清理工具"},
    @{Name="7zip"; Description="压缩解压工具"},
    @{Name="notepadplusplus"; Description="高级文本编辑器"},
    @{Name="tortoisegit"; Description="Git图形化工具"},
    @{Name="sqlitebrowser"; Description="SQLite数据库浏览器"}
)

# 安装核心工具
Write-Host "🔧 安装核心工具..." -ForegroundColor Cyan
foreach ($tool in $coreTools) {
    Write-Host "正在安装 $($tool.Name) - $($tool.Description)..." -ForegroundColor Yellow
    try {
        $result = choco install $tool.Name -y --no-progress 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ $($tool.Name) 安装成功" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  $($tool.Name) 可能已安装或安装遇到问题" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "  ❌ $($tool.Name) 安装失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 询问是否安装可选工具
Write-Host "`n🎯 是否安装可选工具？" -ForegroundColor Cyan
$installOptional = Read-Host "输入 Y 安装可选工具，或按回车跳过 (Y/n)"

if ($installOptional -eq "Y" -or $installOptional -eq "y") {
    Write-Host "🔧 安装可选工具..." -ForegroundColor Cyan
    foreach ($tool in $optionalTools) {
        Write-Host "正在安装 $($tool.Name) - $($tool.Description)..." -ForegroundColor Yellow
        try {
            choco install $tool.Name -y --no-progress | Out-Null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✅ $($tool.Name) 安装成功" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️  $($tool.Name) 可能已安装" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "  ❌ $($tool.Name) 安装失败" -ForegroundColor Red
        }
    }
}

# 使用winget安装PowerToys
Write-Host "`n🔧 安装Microsoft PowerToys..." -ForegroundColor Cyan
try {
    if (Get-Command winget -ErrorAction SilentlyContinue) {
        winget install Microsoft.PowerToys --silent --accept-package-agreements --accept-source-agreements
        Write-Host "✅ PowerToys安装成功" -ForegroundColor Green
    } else {
        Write-Host "⚠️  winget不可用，请手动安装PowerToys" -ForegroundColor Yellow
        Write-Host "下载地址: https://github.com/microsoft/PowerToys/releases" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ PowerToys安装失败" -ForegroundColor Red
}

# 配置Git（如果是首次安装）
Write-Host "`n🔧 配置Git..." -ForegroundColor Cyan
try {
    $gitUser = git config --global user.name 2>$null
    if (-not $gitUser) {
        $userName = Read-Host "请输入Git用户名"
        $userEmail = Read-Host "请输入Git邮箱"
        
        git config --global user.name "$userName"
        git config --global user.email "$userEmail"
        git config --global core.autocrlf true
        git config --global core.editor "code --wait"
        
        Write-Host "✅ Git配置完成" -ForegroundColor Green
    } else {
        Write-Host "✅ Git已配置 (用户: $gitUser)" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Git配置跳过" -ForegroundColor Yellow
}

# 创建项目目录结构
Write-Host "`n📁 创建项目目录结构..." -ForegroundColor Cyan
$projectRoot = "d:\PycharmProjects\my_rdagent\rdagent_integration"
$directories = @(
    "scripts",
    "logs", 
    "data\daily",
    "data\realtime",
    "data\temp",
    "results",
    "backups",
    "reports\performance",
    "reports\complexity",
    "configs"
)

foreach ($dir in $directories) {
    $fullPath = Join-Path $projectRoot $dir
    if (-not (Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "  ✅ 创建目录: $dir" -ForegroundColor Green
    } else {
        Write-Host "  ✅ 目录已存在: $dir" -ForegroundColor Gray
    }
}

# 安装Python包
Write-Host "`n🐍 安装Python依赖包..." -ForegroundColor Cyan
$pythonPackages = @(
    "pandas", "numpy", "scikit-learn", "matplotlib", "seaborn",
    "fastapi", "uvicorn", "pydantic", "requests", "aiohttp",
    "pytest", "flake8", "black", "radon", "coverage"
)

try {
    foreach ($package in $pythonPackages) {
        Write-Host "安装 $package..." -ForegroundColor Yellow
        pip install $package --quiet --disable-pip-version-check
    }
    Write-Host "✅ Python包安装完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️  部分Python包安装可能失败，请稍后手动安装" -ForegroundColor Yellow
}

# 创建快捷启动脚本
Write-Host "`n🚀 创建快捷启动脚本..." -ForegroundColor Cyan
$startupScript = @"
# RDAgent快速启动脚本
Write-Host "🚀 启动RDAgent开发环境..." -ForegroundColor Green

# 启动Everything
if (Get-Process "Everything" -ErrorAction SilentlyContinue) {
    Write-Host "✅ Everything已运行" -ForegroundColor Green
} else {
    Start-Process "Everything" -WindowStyle Minimized -ErrorAction SilentlyContinue
    Write-Host "✅ Everything已启动" -ForegroundColor Green
}

# 启动VS Code
Write-Host "📝 启动VS Code..." -ForegroundColor Cyan
Start-Process "code" -ArgumentList "$projectRoot" -ErrorAction SilentlyContinue

# 启动PowerShell终端
Write-Host "💻 启动PowerShell终端..." -ForegroundColor Cyan
Start-Process "pwsh" -ArgumentList "-NoExit", "-Command", "cd '$projectRoot'"

Write-Host "✅ 开发环境启动完成！" -ForegroundColor Green
"@

$startupScriptPath = Join-Path $projectRoot "scripts\start-development-environment.ps1"
$startupScript | Out-File -FilePath $startupScriptPath -Encoding UTF8
Write-Host "✅ 启动脚本已创建: $startupScriptPath" -ForegroundColor Green

# 创建桌面快捷方式
Write-Host "`n🖥️  创建桌面快捷方式..." -ForegroundColor Cyan
try {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\RDAgent开发环境.lnk")
    $Shortcut.TargetPath = "powershell.exe"
    $Shortcut.Arguments = "-ExecutionPolicy Bypass -File `"$startupScriptPath`""
    $Shortcut.WorkingDirectory = $projectRoot
    $Shortcut.IconLocation = "powershell.exe,0"
    $Shortcut.Description = "RDAgent量化交易平台开发环境"
    $Shortcut.Save()
    Write-Host "✅ 桌面快捷方式已创建" -ForegroundColor Green
} catch {
    Write-Host "⚠️  桌面快捷方式创建失败" -ForegroundColor Yellow
}

# 安装完成总结
Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "🎉 RDAgent工具链安装完成！" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

Write-Host "`n📋 安装总结:" -ForegroundColor Cyan
Write-Host "✅ 核心开发工具已安装" -ForegroundColor Green
Write-Host "✅ 项目目录结构已创建" -ForegroundColor Green  
Write-Host "✅ Python依赖包已安装" -ForegroundColor Green
Write-Host "✅ 快捷启动脚本已创建" -ForegroundColor Green
Write-Host "✅ 桌面快捷方式已创建" -ForegroundColor Green

Write-Host "`n🚀 下一步操作:" -ForegroundColor Yellow
Write-Host "1. 重启终端以使用新安装的工具" -ForegroundColor White
Write-Host "2. 双击桌面'RDAgent开发环境'快捷方式启动" -ForegroundColor White
Write-Host "3. 或运行: .\scripts\start-development-environment.ps1" -ForegroundColor White
Write-Host "4. 访问 http://localhost:8000 查看Web界面" -ForegroundColor White

Write-Host "`n💡 提示:" -ForegroundColor Cyan
Write-Host "- 使用Everything进行超快文件搜索" -ForegroundColor White
Write-Host "- 使用VS Code作为主要开发环境" -ForegroundColor White
Write-Host "- 使用PowerToys增强Windows功能" -ForegroundColor White
Write-Host "- 定期运行健康检查和备份脚本" -ForegroundColor White

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
