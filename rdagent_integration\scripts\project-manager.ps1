# RDAgent项目管理脚本
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "status", "clean", "backup", "health")]
    [string]$Action
)

$ProjectRoot = "d:\PycharmProjects\my_rdagent\rdagent_integration"
$LogDir = "$ProjectRoot\logs"
$DataDir = "$ProjectRoot\data"
$BackupDir = "$ProjectRoot\backups"

# 确保目录存在
@($LogDir, $DataDir, $BackupDir) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ -Force | Out-Null
    }
}

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    Add-Content -Path "$LogDir\project-manager.log" -Value $logEntry
}

function Start-RDAgent {
    Write-Log "🚀 启动RDAgent系统..." "INFO"
    
    # 检查Python环境
    $pythonPath = "$ProjectRoot\venv\Scripts\python.exe"
    if (-not (Test-Path $pythonPath)) {
        $pythonPath = "python"
        if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
            Write-Log "❌ Python环境不存在，请先安装Python" "ERROR"
            return $false
        }
    }
    
    try {
        # 检查端口是否被占用
        $portInUse = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue
        if ($portInUse) {
            Write-Log "⚠️  端口8000已被占用，尝试停止现有服务..." "WARN"
            Stop-RDAgent
            Start-Sleep 3
        }
        
        # 启动Web服务器
        Write-Log "🌐 启动Web服务器..." "INFO"
        $webServerScript = "$ProjectRoot\scripts\start_web_server.py"
        if (Test-Path $webServerScript) {
            Start-Process -FilePath $pythonPath -ArgumentList $webServerScript -WindowStyle Hidden -WorkingDirectory $ProjectRoot
        } else {
            Write-Log "⚠️  Web服务器脚本不存在，创建基础服务器..." "WARN"
            Create-BasicWebServer
            Start-Process -FilePath $pythonPath -ArgumentList "$ProjectRoot\scripts\start_web_server.py" -WindowStyle Hidden -WorkingDirectory $ProjectRoot
        }
        
        # 启动实时数据处理器
        Write-Log "📊 启动实时数据处理器..." "INFO"
        $dataProcessorScript = "$ProjectRoot\scripts\start_realtime_processor.py"
        if (Test-Path $dataProcessorScript) {
            Start-Process -FilePath $pythonPath -ArgumentList $dataProcessorScript -WindowStyle Hidden -WorkingDirectory $ProjectRoot
        } else {
            Write-Log "⚠️  数据处理器脚本不存在，跳过启动" "WARN"
        }
        
        # 等待服务启动
        Write-Log "⏳ 等待服务启动..." "INFO"
        Start-Sleep 5
        
        # 验证服务状态
        $webStatus = Test-NetConnection -ComputerName localhost -Port 8000 -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($webStatus) {
            Write-Log "✅ RDAgent系统启动成功" "SUCCESS"
            Write-Log "🌐 Web界面: http://localhost:8000" "INFO"
            return $true
        } else {
            Write-Log "❌ Web服务启动失败" "ERROR"
            return $false
        }
        
    } catch {
        Write-Log "❌ 启动过程中发生错误: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Stop-RDAgent {
    Write-Log "🛑 停止RDAgent系统..." "INFO"
    
    try {
        # 停止Python进程
        $pythonProcesses = Get-Process python -ErrorAction SilentlyContinue | Where-Object {
            $_.Path -like "*rdagent*" -or 
            $_.MainWindowTitle -like "*RDAgent*" -or
            (Get-NetTCPConnection -OwningProcess $_.Id -ErrorAction SilentlyContinue | Where-Object {$_.LocalPort -eq 8000})
        }
        
        if ($pythonProcesses) {
            Write-Log "🔄 停止 $($pythonProcesses.Count) 个Python进程..." "INFO"
            $pythonProcesses | ForEach-Object {
                Write-Log "停止进程 PID: $($_.Id)" "INFO"
                Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
            }
        }
        
        # 等待进程完全停止
        Start-Sleep 3
        
        # 验证停止状态
        $remainingProcesses = Get-Process python -ErrorAction SilentlyContinue | Where-Object {$_.Path -like "*rdagent*"}
        if (-not $remainingProcesses) {
            Write-Log "✅ RDAgent系统已完全停止" "SUCCESS"
            return $true
        } else {
            Write-Log "⚠️  部分进程可能仍在运行" "WARN"
            return $false
        }
        
    } catch {
        Write-Log "❌ 停止过程中发生错误: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Get-RDAgentStatus {
    Write-Log "📊 检查RDAgent系统状态..." "INFO"
    
    $status = @{
        WebService = $false
        Processes = @()
        PortStatus = @{}
        SystemHealth = "Unknown"
    }
    
    try {
        # 检查Web服务端口
        $webPort = Test-NetConnection -ComputerName localhost -Port 8000 -InformationLevel Quiet -WarningAction SilentlyContinue
        $status.WebService = $webPort
        $status.PortStatus["8000"] = $webPort
        
        # 检查Python进程
        $pythonProcesses = Get-Process python -ErrorAction SilentlyContinue | Where-Object {$_.Path -like "*rdagent*"}
        $status.Processes = $pythonProcesses | ForEach-Object {
            @{
                Name = $_.ProcessName
                PID = $_.Id
                CPU = $_.CPU
                Memory = [math]::Round($_.WorkingSet64 / 1MB, 2)
                StartTime = $_.StartTime
            }
        }
        
        # 系统健康评估
        if ($status.WebService -and $status.Processes.Count -gt 0) {
            $status.SystemHealth = "Healthy"
            Write-Host "🟢 系统状态: 运行中" -ForegroundColor Green
        } elseif ($status.Processes.Count -gt 0) {
            $status.SystemHealth = "Partial"
            Write-Host "🟡 系统状态: 部分运行" -ForegroundColor Yellow
        } else {
            $status.SystemHealth = "Stopped"
            Write-Host "🔴 系统状态: 已停止" -ForegroundColor Red
        }
        
        # 显示详细信息
        Write-Host "`n📊 详细状态信息:" -ForegroundColor Cyan
        Write-Host "🌐 Web服务 (端口8000): $(if($status.WebService){'✅ 运行中'}else{'❌ 未运行'})" -ForegroundColor $(if($status.WebService){'Green'}else{'Red'})
        Write-Host "🐍 Python进程数: $($status.Processes.Count)" -ForegroundColor Cyan
        
        if ($status.Processes.Count -gt 0) {
            Write-Host "`n🔍 运行中的进程:" -ForegroundColor Cyan
            $status.Processes | ForEach-Object {
                Write-Host "  • PID: $($_.PID) | 内存: $($_.Memory) MB | 启动时间: $($_.StartTime)" -ForegroundColor Gray
            }
        }
        
        # 检查日志文件
        $logFiles = Get-ChildItem $LogDir -Filter "*.log" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 5
        if ($logFiles) {
            Write-Host "`n📝 最近的日志文件:" -ForegroundColor Cyan
            $logFiles | ForEach-Object {
                $size = [math]::Round($_.Length / 1KB, 2)
                Write-Host "  • $($_.Name) ($size KB) - $($_.LastWriteTime)" -ForegroundColor Gray
            }
        }
        
        return $status
        
    } catch {
        Write-Log "❌ 状态检查失败: $($_.Exception.Message)" "ERROR"
        return $status
    }
}

function Clear-RDAgentData {
    Write-Log "🧹 清理RDAgent数据..." "INFO"
    
    try {
        $cleanedItems = 0
        $freedSpace = 0
        
        # 清理日志文件（保留最近7天）
        if (Test-Path $LogDir) {
            $cutoffDate = (Get-Date).AddDays(-7)
            $oldLogs = Get-ChildItem $LogDir -Filter "*.log" | Where-Object {$_.LastWriteTime -lt $cutoffDate}
            if ($oldLogs) {
                $oldLogs | ForEach-Object {
                    $freedSpace += $_.Length
                    Remove-Item $_.FullName -Force
                    $cleanedItems++
                }
                Write-Log "✅ 清理了 $($oldLogs.Count) 个旧日志文件" "SUCCESS"
            }
        }
        
        # 清理临时数据
        $tempDirs = @("$DataDir\temp", "$ProjectRoot\__pycache__", "$env:TEMP\rdagent*")
        foreach ($dir in $tempDirs) {
            if (Test-Path $dir) {
                $size = (Get-ChildItem $dir -Recurse -File -ErrorAction SilentlyContinue | Measure-Object Length -Sum).Sum
                Remove-Item $dir -Recurse -Force -ErrorAction SilentlyContinue
                $freedSpace += $size
                $cleanedItems++
                Write-Log "✅ 清理了临时目录: $dir" "SUCCESS"
            }
        }
        
        # 清理Python缓存
        $pycacheCount = 0
        Get-ChildItem $ProjectRoot -Name "__pycache__" -Recurse -Directory -ErrorAction SilentlyContinue | ForEach-Object {
            Remove-Item $_ -Recurse -Force -ErrorAction SilentlyContinue
            $pycacheCount++
        }
        
        Get-ChildItem $ProjectRoot -Name "*.pyc" -Recurse -File -ErrorAction SilentlyContinue | ForEach-Object {
            $freedSpace += $_.Length
            Remove-Item $_ -Force -ErrorAction SilentlyContinue
            $cleanedItems++
        }
        
        if ($pycacheCount -gt 0) {
            Write-Log "✅ 清理了 $pycacheCount 个Python缓存目录" "SUCCESS"
        }
        
        $freedSpaceMB = [math]::Round($freedSpace / 1MB, 2)
        Write-Log "✅ 数据清理完成，共清理 $cleanedItems 个项目，释放 $freedSpaceMB MB 空间" "SUCCESS"
        
        return $true
        
    } catch {
        Write-Log "❌ 数据清理失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Backup-RDAgent {
    Write-Log "💾 开始备份RDAgent项目..." "INFO"
    
    try {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupName = "rdagent_backup_$timestamp"
        $tempBackupPath = "$BackupDir\$backupName"
        
        # 创建临时备份目录
        New-Item -ItemType Directory -Path $tempBackupPath -Force | Out-Null
        
        # 定义要备份的项目
        $itemsToBackup = @(
            @{Source="$ProjectRoot\rdagent_integration"; Dest="rdagent_integration"},
            @{Source="$ProjectRoot\configs"; Dest="configs"},
            @{Source="$ProjectRoot\scripts"; Dest="scripts"},
            @{Source="$ProjectRoot\*.md"; Dest="docs"},
            @{Source="$ProjectRoot\*.json"; Dest="configs"},
            @{Source="$ProjectRoot\*.yml"; Dest="configs"},
            @{Source="$ProjectRoot\*.yaml"; Dest="configs"}
        )
        
        $backedUpFiles = 0
        foreach ($item in $itemsToBackup) {
            if (Test-Path $item.Source) {
                $destPath = "$tempBackupPath\$($item.Dest)"
                New-Item -ItemType Directory -Path $destPath -Force | Out-Null
                
                if ((Get-Item $item.Source).PSIsContainer) {
                    Copy-Item $item.Source -Destination $destPath -Recurse -Force
                } else {
                    Copy-Item $item.Source -Destination $destPath -Force
                }
                $backedUpFiles++
                Write-Log "✅ 已备份: $($item.Source)" "INFO"
            }
        }
        
        # 压缩备份
        $zipPath = "$BackupDir\$backupName.zip"
        Compress-Archive -Path $tempBackupPath -DestinationPath $zipPath -Force
        
        # 清理临时目录
        Remove-Item $tempBackupPath -Recurse -Force
        
        # 清理旧备份（保留最近10个）
        $oldBackups = Get-ChildItem $BackupDir -Filter "rdagent_backup_*.zip" | Sort-Object CreationTime -Descending | Select-Object -Skip 10
        if ($oldBackups) {
            $oldBackups | Remove-Item -Force
            Write-Log "🧹 清理了 $($oldBackups.Count) 个旧备份文件" "INFO"
        }
        
        $backupSize = [math]::Round((Get-Item $zipPath).Length / 1MB, 2)
        Write-Log "✅ 备份完成: $zipPath ($backupSize MB)" "SUCCESS"
        
        return $zipPath
        
    } catch {
        Write-Log "❌ 备份失败: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Test-ProjectHealth {
    Write-Log "🏥 执行项目健康检查..." "INFO"
    
    $healthReport = @{
        Timestamp = Get-Date
        OverallHealth = "Unknown"
        Issues = @()
        Recommendations = @()
        Statistics = @{}
    }
    
    try {
        # 文件统计
        $allFiles = Get-ChildItem $ProjectRoot -Recurse -File -ErrorAction SilentlyContinue
        $healthReport.Statistics["TotalFiles"] = $allFiles.Count
        $healthReport.Statistics["TotalSize"] = [math]::Round(($allFiles | Measure-Object Length -Sum).Sum / 1MB, 2)
        
        # 检查大文件
        $largeFiles = $allFiles | Where-Object {$_.Length -gt 10MB}
        if ($largeFiles) {
            $healthReport.Issues += "发现 $($largeFiles.Count) 个大文件 (>10MB)"
            $healthReport.Recommendations += "考虑压缩或移动大文件到外部存储"
        }
        
        # 检查空目录
        $emptyDirs = Get-ChildItem $ProjectRoot -Recurse -Directory -ErrorAction SilentlyContinue | Where-Object {
            (Get-ChildItem $_.FullName -ErrorAction SilentlyContinue).Count -eq 0
        }
        if ($emptyDirs) {
            $healthReport.Issues += "发现 $($emptyDirs.Count) 个空目录"
            $healthReport.Recommendations += "清理不需要的空目录"
        }
        
        # 检查磁盘空间
        $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='D:'" -ErrorAction SilentlyContinue
        if ($disk) {
            $freePercent = [math]::Round(($disk.FreeSpace / $disk.Size) * 100, 2)
            $healthReport.Statistics["DiskFreePercent"] = $freePercent
            
            if ($freePercent -lt 15) {
                $healthReport.Issues += "磁盘空间不足 ($freePercent%)"
                $healthReport.Recommendations += "清理磁盘空间或扩展存储"
            }
        }
        
        # 评估整体健康状况
        if ($healthReport.Issues.Count -eq 0) {
            $healthReport.OverallHealth = "Excellent"
        } elseif ($healthReport.Issues.Count -le 2) {
            $healthReport.OverallHealth = "Good"
        } elseif ($healthReport.Issues.Count -le 5) {
            $healthReport.OverallHealth = "Fair"
        } else {
            $healthReport.OverallHealth = "Poor"
        }
        
        # 显示结果
        Write-Host "`n🏥 项目健康检查报告:" -ForegroundColor Cyan
        Write-Host "整体健康状况: $($healthReport.OverallHealth)" -ForegroundColor $(
            switch($healthReport.OverallHealth) {
                "Excellent" {"Green"}
                "Good" {"Green"}
                "Fair" {"Yellow"}
                "Poor" {"Red"}
                default {"Gray"}
            }
        )
        
        Write-Host "`n📊 项目统计:" -ForegroundColor Cyan
        Write-Host "  总文件数: $($healthReport.Statistics.TotalFiles)" -ForegroundColor Gray
        Write-Host "  总大小: $($healthReport.Statistics.TotalSize) MB" -ForegroundColor Gray
        if ($healthReport.Statistics.DiskFreePercent) {
            Write-Host "  磁盘剩余: $($healthReport.Statistics.DiskFreePercent)%" -ForegroundColor Gray
        }
        
        if ($healthReport.Issues.Count -gt 0) {
            Write-Host "`n⚠️  发现的问题:" -ForegroundColor Yellow
            $healthReport.Issues | ForEach-Object {
                Write-Host "  • $_" -ForegroundColor Yellow
            }
        }
        
        if ($healthReport.Recommendations.Count -gt 0) {
            Write-Host "`n💡 建议:" -ForegroundColor Cyan
            $healthReport.Recommendations | ForEach-Object {
                Write-Host "  • $_" -ForegroundColor Cyan
            }
        }
        
        # 保存报告
        $reportFile = "$ProjectRoot\reports\health-check-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
        $healthReport | ConvertTo-Json -Depth 3 | Out-File $reportFile -Encoding UTF8
        Write-Log "📄 健康检查报告已保存: $reportFile" "INFO"
        
        return $healthReport
        
    } catch {
        Write-Log "❌ 健康检查失败: $($_.Exception.Message)" "ERROR"
        return $healthReport
    }
}

function Create-BasicWebServer {
    $webServerContent = @'
#!/usr/bin/env python3
"""
RDAgent基础Web服务器
"""
import asyncio
import json
from datetime import datetime
from pathlib import Path

try:
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import HTMLResponse
    import uvicorn
except ImportError:
    print("正在安装必要的依赖包...")
    import subprocess
    subprocess.run(["pip", "install", "fastapi", "uvicorn"], check=True)
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import HTMLResponse
    import uvicorn

app = FastAPI(title="RDAgent管理平台", version="1.0.0")

@app.get("/")
async def root():
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>RDAgent管理平台</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .status { padding: 20px; margin: 20px 0; border-radius: 5px; }
            .status.running { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
            .api-list { margin: 20px 0; }
            .api-item { padding: 10px; margin: 5px 0; background: #e9ecef; border-radius: 3px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 RDAgent量化交易平台</h1>
            <div class="status running">
                <strong>✅ 系统状态:</strong> 运行中<br>
                <strong>🕒 启动时间:</strong> """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """<br>
                <strong>🌐 服务地址:</strong> http://localhost:8000
            </div>
            
            <h2>📡 API接口</h2>
            <div class="api-list">
                <div class="api-item"><strong>GET /api/status</strong> - 系统状态</div>
                <div class="api-item"><strong>GET /api/health</strong> - 健康检查</div>
                <div class="api-item"><strong>GET /api/tasks</strong> - 任务列表</div>
            </div>
            
            <h2>📊 快速链接</h2>
            <div class="api-list">
                <div class="api-item"><a href="/api/status">系统状态 JSON</a></div>
                <div class="api-item"><a href="/api/health">健康检查 JSON</a></div>
            </div>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/api/status")
async def get_status():
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "services": {
            "web_server": "running",
            "data_processor": "unknown"
        }
    }

@app.get("/api/health")
async def health_check():
    return {
        "healthy": True,
        "timestamp": datetime.now().isoformat(),
        "checks": {
            "database": "ok",
            "memory": "ok",
            "disk": "ok"
        }
    }

@app.get("/api/tasks")
async def get_tasks():
    return {
        "tasks": [],
        "total": 0,
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    print("🚀 启动RDAgent Web服务器...")
    print("🌐 访问地址: http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
'@
    
    $webServerPath = "$ProjectRoot\scripts\start_web_server.py"
    $webServerContent | Out-File -FilePath $webServerPath -Encoding UTF8
    Write-Log "✅ 基础Web服务器脚本已创建" "SUCCESS"
}

# 主执行逻辑
switch ($Action) {
    "start" { 
        $result = Start-RDAgent
        if ($result) {
            Write-Host "`n🎉 RDAgent系统启动成功！" -ForegroundColor Green
            Write-Host "🌐 Web界面: http://localhost:8000" -ForegroundColor Cyan
        }
    }
    "stop" { 
        $result = Stop-RDAgent
        if ($result) {
            Write-Host "`n✅ RDAgent系统已完全停止" -ForegroundColor Green
        }
    }
    "status" { 
        Get-RDAgentStatus | Out-Null
    }
    "clean" { 
        $result = Clear-RDAgentData
        if ($result) {
            Write-Host "`n🧹 数据清理完成" -ForegroundColor Green
        }
    }
    "backup" { 
        $backupPath = Backup-RDAgent
        if ($backupPath) {
            Write-Host "`n💾 项目备份完成: $backupPath" -ForegroundColor Green
        }
    }
    "health" {
        $healthReport = Test-ProjectHealth
        Write-Host "`n🏥 健康检查完成，整体状况: $($healthReport.OverallHealth)" -ForegroundColor Green
    }
}

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
