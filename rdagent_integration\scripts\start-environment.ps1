# RDAgent Development Environment Starter
param(
    [string]$ProjectRoot = "d:\PycharmProjects\my_rdagent\rdagent_integration"
)

Write-Host "Starting RDAgent Development Environment..." -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Cyan
Write-Host "Project Path: $ProjectRoot" -ForegroundColor Gray
Write-Host "Start Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

function Write-StartLog {
    param([string]$Message, [string]$Type = "INFO")
    $color = switch($Type) {
        "SUCCESS" {"Green"}
        "WARNING" {"Yellow"}
        "ERROR" {"Red"}
        "INFO" {"Cyan"}
        default {"White"}
    }
    Write-Host $Message -ForegroundColor $color
}

# Check project path
if (-not (Test-Path $ProjectRoot)) {
    Write-StartLog "ERROR: Project path does not exist: $ProjectRoot" "ERROR"
    exit 1
}

Set-Location $ProjectRoot

# Start file search tool (Everything)
Write-StartLog "Starting file search tool..." "INFO"
$everythingPaths = @(
    "C:\Program Files\Everything\Everything.exe",
    "C:\Program Files (x86)\Everything\Everything.exe"
)

$everythingStarted = $false
foreach ($path in $everythingPaths) {
    if (Test-Path $path) {
        try {
            if (-not (Get-Process "Everything" -ErrorAction SilentlyContinue)) {
                Start-Process $path -WindowStyle Minimized
                Write-StartLog "  OK: Everything file search started" "SUCCESS"
            } else {
                Write-StartLog "  OK: Everything already running" "SUCCESS"
            }
            $everythingStarted = $true
            break
        } catch {
            continue
        }
    }
}

if (-not $everythingStarted) {
    Write-StartLog "  WARNING: Everything not installed - recommend installing for fast file search" "WARNING"
    Write-StartLog "    Download: https://www.voidtools.com/" "INFO"
}

# Start file manager
Write-StartLog "Starting file manager..." "INFO"
try {
    Start-Process "explorer.exe" -ArgumentList $ProjectRoot
    Write-StartLog "  OK: File manager opened" "SUCCESS"
} catch {
    Write-StartLog "  ERROR: Failed to open file manager" "ERROR"
}

# Start VS Code
Write-StartLog "Starting VS Code..." "INFO"
try {
    $workspaceFile = "$ProjectRoot\..\rdagent.code-workspace"
    
    if (Get-Command "code" -ErrorAction SilentlyContinue) {
        if (Test-Path $workspaceFile) {
            Start-Process "code" -ArgumentList $workspaceFile
            Write-StartLog "  OK: VS Code workspace opened" "SUCCESS"
        } else {
            Start-Process "code" -ArgumentList $ProjectRoot
            Write-StartLog "  OK: VS Code opened" "SUCCESS"
            
            # Create basic workspace file
            $workspaceContent = @{
                folders = @(
                    @{name = "RDAgent Core"; path = "./rdagent_integration"},
                    @{name = "Scripts"; path = "./rdagent_integration/scripts"},
                    @{name = "Documentation"; path = "."}
                )
                settings = @{
                    "python.defaultInterpreterPath" = "./venv/Scripts/python.exe"
                    "files.exclude" = @{
                        "**/__pycache__" = $true
                        "**/*.pyc" = $true
                    }
                }
            }
            
            $workspaceContent | ConvertTo-Json -Depth 4 | Out-File $workspaceFile -Encoding UTF8
            Write-StartLog "  OK: Workspace configuration created" "SUCCESS"
        }
    } else {
        Write-StartLog "  WARNING: VS Code not installed" "WARNING"
        Write-StartLog "    Recommend installing VS Code for best development experience" "INFO"
    }
} catch {
    Write-StartLog "  ERROR: Failed to start VS Code" "ERROR"
}

# Start PowerShell terminal
Write-StartLog "Starting development terminal..." "INFO"
try {
    if (Get-Command "pwsh" -ErrorAction SilentlyContinue) {
        Start-Process "pwsh" -ArgumentList "-NoExit", "-Command", "cd '$ProjectRoot'; Write-Host 'RDAgent Development Environment Ready' -ForegroundColor Green; Write-Host 'Use: .\scripts\project-status.ps1 -Action status' -ForegroundColor Cyan"
        Write-StartLog "  OK: PowerShell 7 terminal started" "SUCCESS"
    } else {
        Start-Process "powershell" -ArgumentList "-NoExit", "-Command", "cd '$ProjectRoot'; Write-Host 'RDAgent Development Environment Ready' -ForegroundColor Green"
        Write-StartLog "  OK: PowerShell terminal started" "SUCCESS"
    }
} catch {
    Write-StartLog "  ERROR: Failed to start terminal" "ERROR"
}

# Create desktop shortcuts
Write-StartLog "Creating desktop shortcuts..." "INFO"
try {
    $WshShell = New-Object -comObject WScript.Shell
    
    # RDAgent project shortcut
    $projectShortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\RDAgent Project.lnk")
    $projectShortcut.TargetPath = $ProjectRoot
    $projectShortcut.Description = "RDAgent Quantitative Trading Platform"
    $projectShortcut.Save()
    
    # Health check shortcut
    $healthShortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\RDAgent Health Check.lnk")
    $healthShortcut.TargetPath = "powershell.exe"
    $healthShortcut.Arguments = "-ExecutionPolicy Bypass -File `"$ProjectRoot\scripts\health-check.ps1`""
    $healthShortcut.WorkingDirectory = $ProjectRoot
    $healthShortcut.Description = "RDAgent Project Health Check"
    $healthShortcut.Save()
    
    Write-StartLog "  OK: Desktop shortcuts created" "SUCCESS"
} catch {
    Write-StartLog "  WARNING: Failed to create desktop shortcuts" "WARNING"
}

# Show quick start guide
Write-Host "`n" + "=" * 50 -ForegroundColor Cyan
Write-StartLog "Quick Start Guide" "SUCCESS"
Write-Host "=" * 50 -ForegroundColor Cyan

Write-StartLog "`nCommon Commands:" "INFO"
Write-StartLog "  Check Status: .\scripts\project-status.ps1 -Action status" "INFO"
Write-StartLog "  Start System: .\scripts\project-status.ps1 -Action start" "INFO"
Write-StartLog "  Health Check: .\scripts\health-check.ps1" "INFO"
Write-StartLog "  Clean Project: .\scripts\project-status.ps1 -Action clean" "INFO"

Write-StartLog "`nImportant Links:" "INFO"
Write-StartLog "  Web Interface: http://localhost:8000 (after starting system)" "INFO"
Write-StartLog "  Business Guide: .\BUSINESS_WORKFLOW_GUIDE.md" "INFO"
Write-StartLog "  Windows Tools: .\WINDOWS_TOOLS_GUIDE.md" "INFO"

Write-StartLog "`nTips:" "INFO"
Write-StartLog "  - Use Everything for ultra-fast file search" "INFO"
Write-StartLog "  - Use Ctrl+Shift+P in VS Code for command palette" "INFO"
Write-StartLog "  - Run health checks regularly" "INFO"
Write-StartLog "  - Check desktop shortcuts for quick access" "INFO"

Write-Host "`nRDAgent Development Environment Started Successfully!" -ForegroundColor Green
Write-Host "Desktop shortcuts created for quick access." -ForegroundColor Cyan

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
