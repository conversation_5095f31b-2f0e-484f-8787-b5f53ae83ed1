# RDAgent开发环境启动脚本
param(
    [switch]$SkipTools = $false,
    [switch]$Minimal = $false,
    [switch]$ControlPanel = $false
)

$ProjectRoot = "d:\PycharmProjects\my_rdagent\rdagent_integration"

Write-Host "🚀 启动RDAgent开发环境..." -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host "项目路径: $ProjectRoot" -ForegroundColor Gray
Write-Host "启动时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

function Write-StartupLog {
    param([string]$Message, [string]$Type = "INFO")
    $color = switch($Type) {
        "SUCCESS" {"Green"}
        "WARNING" {"Yellow"}
        "ERROR" {"Red"}
        "INFO" {"Cyan"}
        default {"White"}
    }
    Write-Host $Message -ForegroundColor $color
}

function Test-ToolAvailability {
    param([string]$ToolName, [string]$Command)
    
    try {
        $null = Get-Command $Command -ErrorAction Stop
        Write-StartupLog "  ✅ $ToolName 可用" "SUCCESS"
        return $true
    } catch {
        Write-StartupLog "  ❌ $ToolName 不可用" "WARNING"
        return $false
    }
}

function Start-FileSearchTool {
    Write-StartupLog "🔍 启动文件搜索工具..." "INFO"
    
    # 尝试启动Everything
    $everythingPaths = @(
        "C:\Program Files\Everything\Everything.exe",
        "C:\Program Files (x86)\Everything\Everything.exe",
        "$env:LOCALAPPDATA\Programs\Everything\Everything.exe"
    )
    
    $everythingStarted = $false
    foreach ($path in $everythingPaths) {
        if (Test-Path $path) {
            try {
                # 检查是否已经运行
                if (Get-Process "Everything" -ErrorAction SilentlyContinue) {
                    Write-StartupLog "  ✅ Everything已在运行" "SUCCESS"
                } else {
                    Start-Process $path -WindowStyle Minimized
                    Write-StartupLog "  ✅ Everything已启动" "SUCCESS"
                }
                $everythingStarted = $true
                break
            } catch {
                continue
            }
        }
    }
    
    if (-not $everythingStarted) {
        Write-StartupLog "  ⚠️  Everything未安装，建议安装以获得超快文件搜索" "WARNING"
        Write-StartupLog "    下载地址: https://www.voidtools.com/" "INFO"
    }
}

function Start-FileManager {
    Write-StartupLog "📁 启动文件管理器..." "INFO"
    
    # 尝试启动Directory Opus
    $dopusPaths = @(
        "C:\Program Files\GPSoftware\Directory Opus\dopus.exe",
        "C:\Program Files (x86)\GPSoftware\Directory Opus\dopus.exe"
    )
    
    $dopusStarted = $false
    foreach ($path in $dopusPaths) {
        if (Test-Path $path) {
            try {
                Start-Process $path -ArgumentList "/O", "/T", $ProjectRoot
                Write-StartupLog "  ✅ Directory Opus已启动" "SUCCESS"
                $dopusStarted = $true
                break
            } catch {
                continue
            }
        }
    }
    
    if (-not $dopusStarted) {
        # 使用Windows资源管理器
        try {
            Start-Process "explorer.exe" -ArgumentList $ProjectRoot
            Write-StartupLog "  ✅ Windows资源管理器已启动" "SUCCESS"
        } catch {
            Write-StartupLog "  ❌ 无法启动文件管理器" "ERROR"
        }
    }
}

function Start-CodeEditor {
    Write-StartupLog "💻 启动代码编辑器..." "INFO"
    
    # 检查工作区文件
    $workspaceFile = "$ProjectRoot\..\rdagent.code-workspace"
    
    if (Test-ToolAvailability "VS Code" "code") {
        try {
            if (Test-Path $workspaceFile) {
                Start-Process "code" -ArgumentList $workspaceFile
                Write-StartupLog "  ✅ VS Code工作区已启动" "SUCCESS"
            } else {
                Start-Process "code" -ArgumentList $ProjectRoot
                Write-StartupLog "  ✅ VS Code已启动" "SUCCESS"
                
                # 创建基础工作区文件
                $workspaceContent = @{
                    folders = @(
                        @{name = "RDAgent Core"; path = "./rdagent_integration"},
                        @{name = "Scripts"; path = "./rdagent_integration/scripts"},
                        @{name = "Configs"; path = "./rdagent_integration/configs"},
                        @{name = "Docs"; path = "."}
                    )
                    settings = @{
                        "python.defaultInterpreterPath" = "./venv/Scripts/python.exe"
                        "files.exclude" = @{
                            "**/__pycache__" = $true
                            "**/*.pyc" = $true
                            "**/node_modules" = $true
                            "**/.git" = $false
                        }
                    }
                    extensions = @{
                        recommendations = @(
                            "ms-python.python",
                            "ms-python.vscode-pylance",
                            "ms-toolsai.jupyter"
                        )
                    }
                }
                
                $workspaceContent | ConvertTo-Json -Depth 4 | Out-File $workspaceFile -Encoding UTF8
                Write-StartupLog "  ✅ 工作区配置文件已创建" "SUCCESS"
            }
        } catch {
            Write-StartupLog "  ❌ VS Code启动失败" "ERROR"
        }
    } else {
        Write-StartupLog "  ⚠️  VS Code未安装，建议安装以获得最佳开发体验" "WARNING"
    }
}

function Start-Terminal {
    Write-StartupLog "🖥️  启动终端..." "INFO"
    
    # 优先使用PowerShell 7
    if (Test-ToolAvailability "PowerShell 7" "pwsh") {
        try {
            Start-Process "pwsh" -ArgumentList "-NoExit", "-Command", "cd '$ProjectRoot'; Write-Host '🚀 RDAgent开发环境已就绪' -ForegroundColor Green; Write-Host '💡 使用 .\scripts\project-manager.ps1 -Action status 检查系统状态' -ForegroundColor Cyan"
            Write-StartupLog "  ✅ PowerShell 7终端已启动" "SUCCESS"
        } catch {
            Write-StartupLog "  ❌ PowerShell 7启动失败" "ERROR"
        }
    } else {
        # 使用Windows PowerShell
        try {
            Start-Process "powershell" -ArgumentList "-NoExit", "-Command", "cd '$ProjectRoot'; Write-Host '🚀 RDAgent开发环境已就绪' -ForegroundColor Green"
            Write-StartupLog "  ✅ PowerShell终端已启动" "SUCCESS"
        } catch {
            Write-StartupLog "  ❌ 终端启动失败" "ERROR"
        }
    }
}

function Start-SystemMonitoring {
    Write-StartupLog "📊 启动系统监控..." "INFO"
    
    if (-not $Minimal) {
        try {
            # 启动资源监视器
            Start-Process "resmon" -WindowStyle Minimized
            Write-StartupLog "  ✅ 资源监视器已启动" "SUCCESS"
        } catch {
            Write-StartupLog "  ⚠️  资源监视器启动失败" "WARNING"
        }
        
        try {
            # 启动任务管理器（性能选项卡）
            Start-Process "taskmgr" -ArgumentList "/7" -WindowStyle Minimized
            Write-StartupLog "  ✅ 任务管理器已启动" "SUCCESS"
        } catch {
            Write-StartupLog "  ⚠️  任务管理器启动失败" "WARNING"
        }
    }
}

function Start-WebBrowser {
    Write-StartupLog "🌐 准备Web浏览器..." "INFO"
    
    # 检查RDAgent Web服务是否运行
    $webStatus = Test-NetConnection -ComputerName localhost -Port 8000 -InformationLevel Quiet -WarningAction SilentlyContinue
    
    if ($webStatus) {
        try {
            Start-Process "http://localhost:8000"
            Write-StartupLog "  ✅ RDAgent Web界面已打开" "SUCCESS"
        } catch {
            Write-StartupLog "  ⚠️  无法打开Web界面" "WARNING"
        }
    } else {
        Write-StartupLog "  ℹ️  RDAgent Web服务未运行，稍后可手动启动" "INFO"
        Write-StartupLog "    使用命令: .\scripts\project-manager.ps1 -Action start" "INFO"
    }
}

function Show-QuickStartGuide {
    Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
    Write-StartupLog "🎯 快速开始指南" "SUCCESS"
    Write-Host "=" * 60 -ForegroundColor Cyan
    
    Write-StartupLog "`n📋 常用命令:" "INFO"
    Write-StartupLog "  启动系统: .\scripts\project-manager.ps1 -Action start" "INFO"
    Write-StartupLog "  检查状态: .\scripts\project-manager.ps1 -Action status" "INFO"
    Write-StartupLog "  健康检查: .\scripts\project-health-check.ps1" "INFO"
    Write-StartupLog "  控制面板: .\scripts\rdagent-control-panel.ps1" "INFO"
    
    Write-StartupLog "`n🔗 重要链接:" "INFO"
    Write-StartupLog "  Web界面: http://localhost:8000" "INFO"
    Write-StartupLog "  项目文档: .\BUSINESS_WORKFLOW_GUIDE.md" "INFO"
    Write-StartupLog "  工具指南: .\WINDOWS_TOOLS_GUIDE.md" "INFO"
    
    Write-StartupLog "`n💡 提示:" "INFO"
    Write-StartupLog "  • 使用Everything进行超快文件搜索" "INFO"
    Write-StartupLog "  • 使用Ctrl+Shift+P在VS Code中打开命令面板" "INFO"
    Write-StartupLog "  • 定期运行健康检查和备份" "INFO"
    Write-StartupLog "  • 查看日志文件了解系统运行状态" "INFO"
}

function Create-DesktopShortcuts {
    Write-StartupLog "🖥️  创建桌面快捷方式..." "INFO"
    
    try {
        $WshShell = New-Object -comObject WScript.Shell
        
        # RDAgent控制面板快捷方式
        $controlPanelShortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\RDAgent控制面板.lnk")
        $controlPanelShortcut.TargetPath = "powershell.exe"
        $controlPanelShortcut.Arguments = "-ExecutionPolicy Bypass -File `"$ProjectRoot\scripts\rdagent-control-panel.ps1`""
        $controlPanelShortcut.WorkingDirectory = $ProjectRoot
        $controlPanelShortcut.IconLocation = "powershell.exe,0"
        $controlPanelShortcut.Description = "RDAgent量化交易平台控制面板"
        $controlPanelShortcut.Save()
        
        # 项目文件夹快捷方式
        $projectShortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\RDAgent项目.lnk")
        $projectShortcut.TargetPath = $ProjectRoot
        $projectShortcut.Description = "RDAgent量化交易平台项目文件夹"
        $projectShortcut.Save()
        
        Write-StartupLog "  ✅ 桌面快捷方式已创建" "SUCCESS"
        
    } catch {
        Write-StartupLog "  ⚠️  桌面快捷方式创建失败" "WARNING"
    }
}

# 主执行流程
try {
    # 检查项目路径
    if (-not (Test-Path $ProjectRoot)) {
        Write-StartupLog "❌ 项目路径不存在: $ProjectRoot" "ERROR"
        Write-StartupLog "请确认项目路径是否正确" "ERROR"
        exit 1
    }
    
    # 切换到项目目录
    Set-Location $ProjectRoot
    
    # 检查必要的脚本文件
    $requiredScripts = @(
        "scripts\project-manager.ps1",
        "scripts\project-health-check.ps1"
    )
    
    $missingScripts = @()
    foreach ($script in $requiredScripts) {
        if (-not (Test-Path $script)) {
            $missingScripts += $script
        }
    }
    
    if ($missingScripts.Count -gt 0) {
        Write-StartupLog "⚠️  缺少必要的脚本文件:" "WARNING"
        $missingScripts | ForEach-Object {
            Write-StartupLog "  • $_" "WARNING"
        }
        Write-StartupLog "请运行 .\scripts\install-tools.ps1 重新安装" "WARNING"
    }
    
    # 启动各种工具和服务
    if (-not $SkipTools) {
        Start-FileSearchTool
        Start-FileManager
    }
    
    Start-CodeEditor
    Start-Terminal
    
    if (-not $Minimal) {
        Start-SystemMonitoring
        Start-WebBrowser
    }
    
    # 创建桌面快捷方式
    Create-DesktopShortcuts
    
    # 显示快速开始指南
    Show-QuickStartGuide
    
    # 启动控制面板（如果请求）
    if ($ControlPanel) {
        Write-StartupLog "`n🎛️  启动控制面板..." "INFO"
        Start-Sleep 2
        & "$ProjectRoot\scripts\rdagent-control-panel.ps1"
    }
    
    Write-Host "`n🎉 RDAgent开发环境启动完成！" -ForegroundColor Green
    Write-Host "💡 提示: 双击桌面'RDAgent控制面板'快捷方式可快速管理系统" -ForegroundColor Cyan
    
} catch {
    Write-StartupLog "❌ 启动过程中发生错误: $($_.Exception.Message)" "ERROR"
    Write-StartupLog "请检查系统配置或联系技术支持" "ERROR"
    exit 1
}

# 等待用户确认
if (-not $ControlPanel) {
    Write-Host "`n按任意键退出..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
