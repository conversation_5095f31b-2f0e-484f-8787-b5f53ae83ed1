#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试测试脚本 - 专门测试失败的组件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_grid_suitability():
    """测试网格适合度评价器"""
    try:
        import pandas as pd
        import numpy as np
        from core.enhanced_grid_trading_system import GridTradingSuitabilityEvaluator
        
        evaluator = GridTradingSuitabilityEvaluator()
        
        # 创建测试价格序列
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        prices = pd.Series(100 * (1 + np.random.normal(0.001, 0.02, 100)).cumprod(), index=dates)
        
        result = evaluator.evaluate_suitability(prices)
        print(f"✅ 适合度评价成功: {result.overall_score:.3f} - {result.recommendation}")
        return True
        
    except Exception as e:
        print(f"❌ 适合度评价失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_system():
    """测试统一策略管理系统"""
    try:
        from core.unified_strategy_system import UnifiedStrategySystem
        
        # 创建系统实例
        system = UnifiedStrategySystem()
        
        # 检查是否已注册增强网格交易策略
        registered_strategies = system.strategies
        print(f"已注册策略: {list(registered_strategies.keys())}")
        
        if 'EnhancedGridTrading' in registered_strategies:
            print("✅ 增强网格交易策略已注册")
            
            # 尝试创建策略实例
            strategy_params = {
                'grid_type': 'symmetric',
                'grid_density': 'high',
                'enable_suitability_filter': True,
                'enable_dynamic_adjustment': True
            }
            
            strategy = system.create_strategy('EnhancedGridTrading', strategy_params)
            if strategy:
                print("✅ 策略创建成功")
                return True
            else:
                print("❌ 策略创建失败")
                return False
        else:
            print("❌ 增强网格交易策略未注册")
            return False
            
    except Exception as e:
        print(f"❌ 统一系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== 调试测试开始 ===")
    
    results = {
        '网格适合度评价器': test_grid_suitability(),
        '统一策略管理系统': test_unified_system()
    }
    
    print("\n=== 测试结果 ===")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  - {test_name}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    print(f"\n🎯 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)