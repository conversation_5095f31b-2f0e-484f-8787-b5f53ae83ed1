<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .header .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .nav {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }
        
        .nav a {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav a:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 500;
            color: #34495e;
        }
        
        .status-value {
            font-weight: bold;
        }
        
        .status-good { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-critical { color: #e74c3c; }
        
        .health-score {
            text-align: center;
            margin: 20px 0;
        }
        
        .health-score .score {
            font-size: 3em;
            font-weight: bold;
            color: #f39c12;
        }
        
        .health-score .label {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-top: 10px;
        }
        
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .recommendations h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .recommendations ul {
            list-style-type: none;
        }
        
        .recommendations li {
            color: #856404;
            margin: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .recommendations li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }
        
        .quick-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .action-btn {
            background: #2ecc71;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .action-btn:hover {
            background: #27ae60;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
        }
        
        .action-btn.secondary {
            background: #95a5a6;
        }
        
        .action-btn.secondary:hover {
            background: #7f8c8d;
        }
        
        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 40px;
            padding: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .nav {
                flex-direction: column;
                align-items: center;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 RDAgent 管理控制台</h1>
            <p class="subtitle">量化交易平台 - Windows高效项目管理系统</p>
            <div class="nav">
                <a href="/">📊 仪表板</a>
                <a href="/health">🏥 健康检查</a>
                <a href="/files">📁 文件管理</a>
                <a href="/control">⚙️ 系统控制</a>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <!-- System Status Card -->
            <div class="card">
                <h3>🖥️ 系统状态</h3>
                {% if system_status.error %}
                    <p class="status-critical">错误: {{ system_status.error }}</p>
                {% else %}
                    <div class="status-item">
                        <span class="status-label">内存使用率</span>
                        <span class="status-value status-{{ system_status.memory.status }}">
                            {{ system_status.memory.percent }}%
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">可用内存</span>
                        <span class="status-value">{{ system_status.memory.available_gb }} GB</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">磁盘使用率</span>
                        <span class="status-value status-{{ system_status.disk.status }}">
                            {{ system_status.disk.used_percent }}%
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">CPU使用率</span>
                        <span class="status-value status-{{ system_status.cpu.status }}">
                            {{ system_status.cpu.percent }}%
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Python进程</span>
                        <span class="status-value">{{ system_status.processes.python_count }}</span>
                    </div>
                {% endif %}
            </div>
            
            <!-- Project Status Card -->
            <div class="card">
                <h3>📁 项目状态</h3>
                {% if system_status.project %}
                    <div class="status-item">
                        <span class="status-label">文件总数</span>
                        <span class="status-value">{{ system_status.project.file_count }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">项目路径</span>
                        <span class="status-value" style="font-size: 0.9em;">{{ system_status.project.path }}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">最后更新</span>
                        <span class="status-value">{{ system_status.timestamp[:19] }}</span>
                    </div>
                {% endif %}
            </div>
            
            <!-- Health Status Card -->
            <div class="card">
                <h3>🏥 健康状况</h3>
                {% if health_status.error %}
                    <p class="status-critical">错误: {{ health_status.error }}</p>
                {% else %}
                    <div class="health-score">
                        <div class="score">{{ health_status.overall_score }}%</div>
                        <div class="label">{{ health_status.status }}</div>
                    </div>
                    
                    {% for check_name, check_data in health_status.checks.items() %}
                        <div class="status-item">
                            <span class="status-label">{{ check_name.replace('_', ' ').title() }}</span>
                            <span class="status-value status-{{ check_data.status }}">
                                {{ check_data.status.upper() }}
                            </span>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        
        <!-- Recommendations -->
        {% if health_status.recommendations %}
        <div class="card">
            <div class="recommendations">
                <h4>💡 优化建议</h4>
                <ul>
                    {% for recommendation in health_status.recommendations %}
                        <li>{{ recommendation }}</li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}
        
        <!-- Quick Actions -->
        <div class="card">
            <h3>⚡ 快速操作</h3>
            <div class="quick-actions">
                <button class="action-btn" onclick="runHealthCheck()">🏥 运行健康检查</button>
                <button class="action-btn" onclick="cleanProject()">🧹 清理项目</button>
                <a href="/files" class="action-btn secondary">📁 浏览文件</a>
                <a href="/control" class="action-btn secondary">⚙️ 系统控制</a>
            </div>
        </div>
        
        <div class="footer">
            <p>RDAgent Management Console v2.0 | 最后更新: {{ system_status.timestamp[:19] if system_status.timestamp else 'Unknown' }}</p>
        </div>
    </div>
    
    <script>
        async function runHealthCheck() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '🔄 运行中...';
            
            try {
                const response = await fetch('/api/action/health_check', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    alert('健康检查完成！\n\n' + result.output.substring(0, 500) + '...');
                    location.reload();
                } else {
                    alert('健康检查失败: ' + result.error);
                }
            } catch (error) {
                alert('操作失败: ' + error.message);
            } finally {
                btn.disabled = false;
                btn.textContent = '🏥 运行健康检查';
            }
        }
        
        async function cleanProject() {
            if (!confirm('确定要清理项目文件吗？这将删除临时文件和缓存。')) {
                return;
            }
            
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '🔄 清理中...';
            
            try {
                const response = await fetch('/api/action/clean_project', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    alert('项目清理完成！');
                    location.reload();
                } else {
                    alert('清理失败: ' + result.error);
                }
            } catch (error) {
                alert('操作失败: ' + error.message);
            } finally {
                btn.disabled = false;
                btn.textContent = '🧹 清理项目';
            }
        }
        
        // Auto-refresh every 30 seconds
        setInterval(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
